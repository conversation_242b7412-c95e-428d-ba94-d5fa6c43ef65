import React from 'react';
import { Card, CardContent, Input } from '../ui';
import { FaTimes } from 'react-icons/fa';

interface ProjectsFiltersProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filteredCount: number;
  totalCount: number;
  onClearFilters: () => void;
}

const ProjectsFilters: React.FC<ProjectsFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  filteredCount,
  totalCount,
  onClearFilters
}) => {

  const hasActiveFilters = searchTerm

  return (
    <div className="mb-12">
      <Card className="glass rounded-2xl">
        <CardContent className="p-8">
          <div className="space-y-6 md:space-y-0 md:flex md:items-center md:space-x-6">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <span className="text-purple-500 text-xl" aria-hidden="true">🔍</span>
                </div>
                <Input
                  placeholder="Search projects, technologies, or keywords..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 bg-white/80 border-purple-200 focus:border-purple-400 rounded-xl"
                  aria-label="Search projects"
                />
              </div>
            </div>
          </div>

          {/* Results Summary */}
          <div className="mt-6 pt-6 border-t border-white/20">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                Showing <span className="font-bold text-purple-600">{filteredCount}</span> of <span className="font-bold">{totalCount}</span> projects
              </span>
              {hasActiveFilters && (
                <button
                  onClick={onClearFilters}
                  className="flex items-center space-x-2 text-purple-600 hover:text-purple-800 transition-colors font-medium"
                  aria-label="Clear all filters"
                >
                  <FaTimes className="text-xs" />
                  <span>Clear Filters</span>
                </button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectsFilters;
