import React, { useRef } from 'react';
import { FaSearch, FaTimes, FaFilter, FaCalendarAlt } from 'react-icons/fa';

interface ContactFilters {
  text?: string;
  status?: string;
  source?: string;
  priority?: string;
  enquiryType?: string;
  startDate?: string;
  endDate?: string;
}

interface ContactsFilterProps {
  filters: ContactFilters;
  onFiltersChange: (filters: Partial<ContactFilters>) => void;
  onApplyFilters: () => void;
  onResetFilters: () => Promise<void>;
  loading?: boolean;
}

const ContactsFilter: React.FC<ContactsFilterProps> = ({
  filters,
  onFiltersChange,
  onApplyFilters,
  onResetFilters,
  loading = false
}) => {
  const textInputRef = useRef<HTMLInputElement>(null);
  const [clearLoading, setClearLoading] = React.useState(false);

  // Handle text input change (no automatic filtering)
  const handleTextChange = (value: string) => {
    onFiltersChange({ text: value });
  };

  // Handle dropdown changes (no automatic filtering)
  const handleStatusChange = (status: string) => {
    onFiltersChange({ status });
  };

  const handleSourceChange = (source: string) => {
    onFiltersChange({ source });
  };

  const handlePriorityChange = (priority: string) => {
    onFiltersChange({ priority });
  };

  const handleEnquiryTypeChange = (enquiryType: string) => {
    onFiltersChange({ enquiryType });
  };

  const handleStartDateChange = (startDate: string) => {
    // Ensure date is in yyyy-mm-dd format
    const formattedDate = startDate ? formatDateForAPI(startDate) : '';
    onFiltersChange({ startDate: formattedDate });
  };

  const handleEndDateChange = (endDate: string) => {
    // Ensure date is in yyyy-mm-dd format
    const formattedDate = endDate ? formatDateForAPI(endDate) : '';
    onFiltersChange({ endDate: formattedDate });
  };

  // Format date to yyyy-mm-dd format
  const formatDateForAPI = (dateString: string): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onApplyFilters();
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onApplyFilters();
    }
  };

  // Handle clear filters with loading state
  const handleClearFilters = async () => {
    setClearLoading(true);
    try {
      await onResetFilters();
    } finally {
      setClearLoading(false);
    }
  };

  // Clear text filter
  const clearTextFilter = () => {
    onFiltersChange({ text: '' });
    if (textInputRef.current) {
      textInputRef.current.focus();
    }
  };

  // Check if any filters are active
  const hasActiveFilters = 
    filters.status !== 'ALL' || 
    filters.source !== 'ALL' || 
    filters.priority !== 'ALL' || 
    filters.enquiryType !== 'ALL' || 
    (filters.text && filters.text.trim() !== '') ||
    (filters.startDate && filters.startDate !== '') ||
    (filters.endDate && filters.endDate !== '');

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center mb-4">
        <div className="flex items-center space-x-2">
          <FaFilter className="text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-900">Filter Contacts</h3>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* First Row - Text Search */}
        <div className="grid grid-cols-1 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Search by Name, Email, or Phone
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="h-4 w-4 text-gray-400" />
              </div>
              <input
                ref={textInputRef}
                type="text"
                value={filters.text || ''}
                onChange={(e) => handleTextChange(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Start typing to search..."
                className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
                disabled={loading}
              />
              {filters.text && (
                <button
                  type="button"
                  onClick={clearTextFilter}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 transition-colors duration-200"
                  disabled={loading}
                >
                  <FaTimes className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Second Row - Dropdowns */}
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4">
          {/* Status Dropdown */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              value={filters.status || 'ALL'}
              onChange={(e) => handleStatusChange(e.target.value)}
              className="block w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              <option value="ALL">All Status</option>
              <option value="NEW">New</option>
              <option value="INPROGRESS">In Progress</option>
              <option value="RESOLVING">Resolving</option>
              <option value="CLOSED">Closed</option>
            </select>
          </div>

          {/* Source Dropdown */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Source
            </label>
            <select
              value={filters.source || 'ALL'}
              onChange={(e) => handleSourceChange(e.target.value)}
              className="block w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              <option value="ALL">All Sources</option>
              <option value="WEBSITE">Website</option>
              <option value="PHONE">Phone</option>
              <option value="REFERAL">Referral</option>
              <option value="SOCIAL">Social</option>
            </select>
          </div>

          {/* Priority Dropdown */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Priority
            </label>
            <select
              value={filters.priority || 'ALL'}
              onChange={(e) => handlePriorityChange(e.target.value)}
              className="block w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              <option value="ALL">All Priorities</option>
              <option value="LOW">Low</option>
              <option value="MEDIUM">Medium</option>
              <option value="HIGH">High</option>
              <option value="CRITICAL">Critical</option>
            </select>
          </div>

          {/* Enquiry Type Dropdown */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Contact Type
            </label>
            <select
              value={filters.enquiryType || 'ALL'}
              onChange={(e) => handleEnquiryTypeChange(e.target.value)}
              className="block w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
              disabled={loading}
            >
              <option value="ALL">All Types</option>
              <option value="GENERAL">General</option>
              <option value="INTERNSHIP">Internship</option>
              <option value="PARTNERSHIP">Partnership</option>
              <option value="TECHNICAL">Technical</option>
            </select>
          </div>
        </div>

        {/* Third Row - Date Range and Actions */}
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 items-end">
          {/* Start Date */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              From Date
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaCalendarAlt className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="date"
                value={filters.startDate || ''}
                onChange={(e) => handleStartDateChange(e.target.value)}
                className="block w-full pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
                disabled={loading}
              />
            </div>
          </div>

          {/* End Date */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              To Date
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaCalendarAlt className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="date"
                value={filters.endDate || ''}
                onChange={(e) => handleEndDateChange(e.target.value)}
                className="block w-full pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
                disabled={loading}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Actions
            </label>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
              <button
                type="submit"
                disabled={loading}
                className="flex-1 inline-flex items-center justify-center px-4 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    <span className="hidden sm:inline">Filtering...</span>
                    <span className="sm:hidden">Loading...</span>
                  </>
                ) : (
                  <>
                    <FaSearch className="mr-2" />
                    Apply
                  </>
                )}
              </button>

              {hasActiveFilters && (
                <button
                  type="button"
                  onClick={handleClearFilters}
                  disabled={loading || clearLoading}
                  className="flex-1 sm:flex-none inline-flex items-center justify-center px-4 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  {clearLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                      <span className="hidden sm:inline">Clearing...</span>
                      <span className="sm:hidden">Loading...</span>
                    </>
                  ) : (
                    <>
                      <FaTimes className="mr-2" />
                      <span className="hidden sm:inline">Clear Filter</span>
                      <span className="sm:hidden">Clear</span>
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </form>

      {/* Loading Indicator */}
      {loading && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
            <span>Filtering contacts...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContactsFilter;
