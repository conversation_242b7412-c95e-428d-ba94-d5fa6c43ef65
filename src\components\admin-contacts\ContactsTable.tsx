import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '../ui';
import { Contact } from '../../types/contact';
import ContactRow from './ContactRow';
import EmptyState from './EmptyState';
import {
  <PERSON>a<PERSON>ser,
  FaEnvelope,
  FaPhone,
  FaEdit,
  FaTrash,
  FaEye,
  FaClock,
  FaExclamationTriangle,
  FaCheckCircle
} from 'react-icons/fa';

interface ContactsTableProps {
  contacts: Contact[];
  onViewContact: (contact: Contact) => void;
  onEditContact: (contact: Contact) => void;
  onDeleteContact: (contactId: number) => void;
}

// Helper functions for mobile view
const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'high':
      return 'bg-red-100 text-red-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'low':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getPriorityIcon = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'high':
      return <FaExclamationTriangle className="text-red-500" />;
    case 'medium':
      return <FaClock className="text-yellow-500" />;
    case 'low':
      return <FaCheckCircle className="text-green-500" />;
    default:
      return <FaClock className="text-gray-500" />;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Mobile Card Component
const ContactCard: React.FC<{
  contact: Contact;
  onView: (contact: Contact) => void;
  onEdit: (contact: Contact) => void;
  onDelete: (contactId: number) => void;
}> = ({ contact, onView, onEdit, onDelete }) => {
  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center flex-shrink-0">
              <FaUser className="text-white text-sm" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-900">{contact.name}</h4>
              <p className="text-xs text-gray-500">ID: {contact.id}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {getPriorityIcon(contact.priority)}
            <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(contact.priority)}`}>
              {contact.priority}
            </span>
          </div>
        </div>

        <div className="space-y-2 mb-4">
          <div className="flex items-center space-x-2">
            <FaEnvelope className="text-gray-400 text-xs" />
            <span className="text-sm text-gray-600 truncate">{contact.email}</span>
          </div>
          {contact.phone && (
            <div className="flex items-center space-x-2">
              <FaPhone className="text-gray-400 text-xs" />
              <span className="text-sm text-gray-600">{contact.phone}</span>
            </div>
          )}
          <div className="text-sm text-gray-900 font-medium">
            Subject: {contact.subject}
          </div>
          <div className="text-xs text-gray-500">
            Status: {contact.status} • Created: {formatDate(contact.createdAt)}
          </div>
          <div className="text-xs text-gray-500">
            Source: {contact.source.replace('_', ' ')}
          </div>
        </div>

        <div className="pt-3 border-t border-gray-100">
          {/* Action buttons - Stack on small screens, inline on larger screens */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex flex-wrap items-center gap-2">
              <Button
                onClick={() => onView(contact)}
                variant="outline"
                size="sm"
                className="text-blue-600 border-blue-200 hover:bg-blue-50 flex-shrink-0"
              >
                <FaEye className="w-3 h-3 mr-1" />
                View
              </Button>
              <Button
                onClick={() => onEdit(contact)}
                variant="outline"
                size="sm"
                className="text-green-600 border-green-200 hover:bg-green-50 flex-shrink-0"
              >
                <FaEdit className="w-3 h-3 mr-1" />
                Edit
              </Button>
            </div>
            <div className="flex items-center justify-center sm:justify-end space-x-2">
              <a
                href={`mailto:${contact.email}`}
                className="p-2 text-purple-600 hover:bg-purple-50 rounded-lg transition-colors flex-shrink-0"
                title="Send Email"
              >
                <FaEnvelope className="w-3 h-3" />
              </a>
              {contact.phone && (
                <a
                  href={`tel:${contact.phone}`}
                  className="p-2 text-orange-600 hover:bg-orange-50 rounded-lg transition-colors flex-shrink-0"
                  title="Call"
                >
                  <FaPhone className="w-3 h-3" />
                </a>
              )}
              <button
                onClick={() => onDelete(contact.id)}
                className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors flex-shrink-0"
                title="Delete"
              >
                <FaTrash className="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const ContactsTable: React.FC<ContactsTableProps> = ({
  contacts,
  onViewContact,
  onEditContact,
  onDeleteContact
}) => {
  if (contacts.length === 0) {
    return <EmptyState />;
  }

  return (
    <>
      {/* Desktop Table View - Only show on very wide screens */}
      <div className="hidden 2xl:block">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Contacts ({contacts.length})
              </h3>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Subject
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {contacts.map((contact) => (
                    <ContactRow
                      key={contact.id}
                      contact={contact}
                      onView={onViewContact}
                      onEdit={onEditContact}
                      onDelete={onDeleteContact}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Card View - Show on all screens except very wide ones */}
      <div className="2xl:hidden">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Contacts ({contacts.length})
          </h3>
        </div>
        <div className="space-y-4">
          {contacts.map((contact) => (
            <ContactCard
              key={contact.id}
              contact={contact}
              onView={onViewContact}
              onEdit={onEditContact}
              onDelete={onDeleteContact}
            />
          ))}
        </div>
      </div>
    </>
  );
};

export default ContactsTable;
