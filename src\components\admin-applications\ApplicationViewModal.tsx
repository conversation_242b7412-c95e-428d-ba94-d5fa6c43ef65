import React, { useState } from 'react';
import { <PERSON>dal, <PERSON><PERSON>, Card, CardContent, CardHeader, Textarea, Select } from '../ui';
import {
  FaUser,
  FaGraduationCap,
  FaCode,
  FaDownload,
  FaCheckCircle,
  FaPhone,
  FaEnvelope,
  FaCalendarAlt,
  FaClock,
  FaStickyNote,
  FaPlus,
  FaTrash,
  FaUserPlus,
  FaBan
} from 'react-icons/fa';
import { InternApplicant, ApplicantNote, AddApplicantNoteRequest } from '../../services/internService';
import internService from '../../services/internService';

interface ApplicationViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdateStatus: (applicationId: number, status: InternApplicant['applicationStatus'], notes?: string) => void;
  application: InternApplicant | null;
  onAddNote: (id: number, notes: AddApplicantNoteRequest[]) => void;
  onDeleteNote: (noteId: number, applicantId: number) => void;
  onDeleteApplication: (applicationId: number) => void;
  onAddAsIntern: (applicationId: number) => void;
}

const ApplicationViewModal: React.FC<ApplicationViewModalProps> = ({
  isOpen,
  onClose,
  onUpdateStatus,
  application,
  onAddNote,
  onDeleteNote,
  onDeleteApplication,
  onAddAsIntern
}) => {
  const [newNote, setNewNote] = useState('');
  const [noteType, setNoteType] = useState<ApplicantNote['type']>('NOTE');
  const [showAddNote, setShowAddNote] = useState(false);

  if (!application) return null;

  const formatDate = (dateString: string) => {
    return internService.formatDate(dateString);
  };

  const getStatusColor = (status: InternApplicant['applicationStatus']) => {
    return internService.getApplicationStatusColor(status);
  };

  const handleAddNote = async () => {
    if (!newNote.trim()) return;

    try {
      await onAddNote(application.id, [{
        activityType: noteType as AddApplicantNoteRequest['activityType'],
        text: newNote.trim()
      }]);
      setNewNote('');
      setShowAddNote(false);
    } catch (error) {
      console.error('Failed to add note:', error);
    }
  };

  const handleDeleteNote = async (noteId: number) => {
    try {
      await onDeleteNote(noteId, application.id);
    } catch (error) {
      console.error('Failed to delete note:', error);
    }
  };

  const getNoteIcon = (type: ApplicantNote['type']) => {
    switch (type) {
      case 'CALL': return <FaPhone className="text-blue-500" />;
      case 'EMAIL': return <FaEnvelope className="text-green-500" />;
      case 'MEETING': return <FaCalendarAlt className="text-purple-500" />;
      case 'FOLLOW_UP': return <FaClock className="text-orange-500" />;
      case 'NOTE': return <FaStickyNote className="text-blue-500" />;
      case 'GENERAL': return <FaStickyNote className="text-gray-500" />;
      default: return <FaStickyNote className="text-gray-500" />;
    }
  };





  const handleStatusUpdate = (status: InternApplicant['applicationStatus']) => {
    onUpdateStatus(application.id, status);
    onClose();
  };

  const handleResumeDownload = async (id: number,name: string) => {
    try {
      await internService.downloadResume(id,name);
    } catch (error) {
      console.error('Failed to download resume:', error);
      // You could add a toast notification here
      alert('Failed to download resume. Please try again.');
    }
  };

  return (
    <Modal title="Application Management" isOpen={isOpen} onClose={onClose} size="xl">
      <div className="max-h-[80vh] overflow-y-auto space-y-6">
        {/* Application Header */}
        <div className="flex items-start justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
              <FaUser className="text-white text-xl" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{application.name}</h2>
              <p className="text-lg text-gray-600">{application.projectName}</p>
              <p className="text-sm text-gray-500">Applied on {formatDate(application.appliedAt)}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(application.applicationStatus)}`}>
              {internService.getApplicationStatusDisplayName(application.applicationStatus)}
            </span>
          </div>
        </div>

        {/* Application Actions */}
        <div className="p-4 bg-white border border-gray-200 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-gray-700">Application Actions</h4>
            <div className="flex items-center space-x-2">
              <a
                href={`tel:${application.phone}`}
                className="flex items-center space-x-1 px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
              >
                <FaPhone />
                <span>Call</span>
              </a>
              <a
                href={`mailto:${application.email}`}
                className="flex items-center space-x-1 px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
              >
                <FaEnvelope />
                <span>Email</span>
              </a>
            </div>
          </div>

          {/* Status Change Buttons */}
          <div className="mb-4">
            <h5 className="text-xs font-medium text-gray-600 mb-2">Change Status</h5>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <Button
                onClick={() => handleStatusUpdate('PENDING')}
                className={`flex items-center justify-center space-x-1 text-xs ${
                  application.applicationStatus === 'PENDING'
                    ? 'bg-yellow-600 hover:bg-yellow-700 cursor-not-allowed opacity-75'
                    : 'bg-yellow-500 hover:bg-yellow-600'
                } text-white`}
                size="sm"
                disabled={application.applicationStatus === 'PENDING'}
              >
                <FaClock />
                <span>Pending</span>
              </Button>

              <Button
                onClick={() => handleStatusUpdate('REVIEWING')}
                className={`flex items-center justify-center space-x-1 text-xs ${
                  application.applicationStatus === 'REVIEWING'
                    ? 'bg-blue-600 hover:bg-blue-700 cursor-not-allowed opacity-75'
                    : 'bg-blue-500 hover:bg-blue-600'
                } text-white`}
                size="sm"
                disabled={application.applicationStatus === 'REVIEWING'}
              >
                <FaCheckCircle />
                <span>Reviewing</span>
              </Button>

              <Button
                onClick={() => handleStatusUpdate('ACCEPTED')}
                className={`flex items-center justify-center space-x-1 text-xs ${
                  application.applicationStatus === 'ACCEPTED'
                    ? 'bg-green-600 hover:bg-green-700 cursor-not-allowed opacity-75'
                    : 'bg-green-500 hover:bg-green-600'
                } text-white`}
                size="sm"
                disabled={application.applicationStatus === 'ACCEPTED'}
              >
                <FaUserPlus />
                <span>Accept</span>
              </Button>

              <Button
                onClick={() => handleStatusUpdate('REJECTED')}
                className={`flex items-center justify-center space-x-1 text-xs ${
                  application.applicationStatus === 'REJECTED'
                    ? 'bg-red-600 hover:bg-red-700 cursor-not-allowed opacity-75'
                    : 'bg-red-500 hover:bg-red-600'
                } text-white`}
                size="sm"
                disabled={application.applicationStatus === 'REJECTED'}
              >
                <FaBan />
                <span>Rejected</span>
              </Button>
            </div>
          </div>

          {/* Main Actions */}
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={() => onAddAsIntern(application.id)}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white"
              size="sm"
            >
              <FaUserPlus />
              <span>Add as Intern</span>
            </Button>

            <Button
              onClick={() => onDeleteApplication(application.id)}
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white"
              size="sm"
            >
              <FaTrash />
              <span>Delete Application</span>
            </Button>
          </div>
        </div>

        {/* Student Details */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <FaUser className="text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">Student Details</h3>
            </div>
          </CardHeader>
          <CardContent>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg">
            <div>
              <p className="text-sm font-medium text-gray-700">Email</p>
              <p className="text-gray-900">{application.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Phone</p>
              <p className="text-gray-900">{application.phone}</p>
            </div>
             <div>
              <p className="text-sm font-medium text-gray-700">Application Id</p>
              <p className="text-gray-900">{application.id}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Resume</p>
              <button
                onClick={() => handleResumeDownload(application.id,application.name)}
                className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 transition-colors"
              >
                <FaDownload />
                <span>Download Resume</span>
              </button>
            </div>
          </div>
          </CardContent>
        </Card>

        {/* Programming Experience */}
        <div className="mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <FaCode className="text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Programming Experience</h3>
          </div>
          
          <div className="p-4 bg-green-50 rounded-lg">


            <div className="mb-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Skills</p>
              <div className="flex flex-wrap gap-2">
                {application.skills && application.skills.length > 0 ? (
                  application.skills.map((skill: string, index: number) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-green-100 text-green-800 text-sm rounded-full"
                    >
                      {skill}
                    </span>
                  ))
                ) : (
                  <p className="text-gray-500">No skills provided</p>
                )}
              </div>
            </div>

            <div className="mb-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Technologies</p>
              <div className="flex flex-wrap gap-2">
                {application.technologies.map((tech: string, index: number) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>


          </div>
        </div>

        {/* Prerequisite Answers */}
        <div className="mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <FaGraduationCap className="text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">Prerequisite Questions & Answers</h3>
          </div>
          
          <div className="space-y-4">
            {application.internAnswers.map((qa, index) => (
              <div key={index} className="p-4 bg-purple-50 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{qa.question}</h4>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    qa.required ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {qa.required ? 'Required' : 'Optional'}
                  </span>
                </div>
                <div className="text-gray-700">
                  <strong>Answer:</strong> {qa.multiSelectAnswer.length > 0 ? qa.multiSelectAnswer.join(', ') : qa.answer}
                </div>
                {qa.questionType && (
                  <div className="text-sm text-gray-500 mt-1">
                    Type: {qa.questionType}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>



        {/* Accept Action Info */}
        {application.status !== 'accepted' && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-800 mb-2">💡 What happens when you accept this application?</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Student will be automatically added as an intern to the project team</li>
              <li>• Intern account will be created in the user management system</li>
              <li>• Project team members will be notified of the new intern</li>
              <li>• Welcome email with project details will be sent to the student</li>
            </ul>
          </div>
        )}

        {/* Notes & Activity */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FaStickyNote className="text-purple-600" />
                <h3 className="text-lg font-semibold text-gray-900">Notes & Activity</h3>
              </div>
              <Button
                onClick={() => setShowAddNote(!showAddNote)}
                size="sm"
                className="flex items-center space-x-1"
              >
                <FaPlus />
                <span>Add Note</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Add Note Form */}
            {showAddNote && (
              <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <Select
                    label="Activity Type"
                    value={noteType}
                    onChange={(e) => setNoteType(e.target.value as ApplicantNote['type'])}
                    options={internService.getNoteTypeOptions()}
                  />
                </div>
                <Textarea
                  label="Note Content"
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  placeholder="Enter your note or activity details..."
                  rows={3}
                />
                <div className="flex justify-end space-x-2 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowAddNote(false)}
                    size="sm"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleAddNote}
                    disabled={!newNote.trim()}
                    size="sm"
                  >
                    Add Note
                  </Button>
                </div>
              </div>
            )}

            {/* Notes List */}
            <div className="space-y-4">
              {application.applicantNotes.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No notes or activities yet. Add the first one above.</p>
              ) : (
                application.applicantNotes.map((note) => (
                  <div key={note.id} className="flex items-start space-x-3 p-4 bg-white border border-gray-200 rounded-lg">
                    <div className="flex-shrink-0 mt-1">
                      {getNoteIcon(note.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">
                            {internService.getNoteTypeDisplayName(note.type)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleDeleteNote(note.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <FaTrash className="text-xs" />
                          </button>
                        </div>
                      </div>
                      <p className="text-gray-700 text-sm">{note.text}</p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </Modal>
  );
};

export default ApplicationViewModal;
