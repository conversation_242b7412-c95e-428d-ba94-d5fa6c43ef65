import React, { useState } from 'react';
import {
  FaPhone,
  FaEnvelope,
  FaUser,
  FaCalendarAlt,
  FaClock,
  FaStickyNote,
  FaPlus,
  FaTrash
} from 'react-icons/fa';
import { <PERSON>ton, Card, CardContent, CardHeader, Textarea, Select, Modal } from '../ui';
import { Contact, ContactNote } from '../../types/contact';
import contactService, { AddNoteRequest } from '../../services/contactService';

interface ContactDetailModalProps {
  contact: Contact;
  isOpen: boolean;
  onClose: () => void;
  onUpdateContact: (contact: Contact) => void;
  onAddNote: (contactId: number, notes: AddNoteRequest[]) => void;
  onDeleteNote: (contactId: number, noteId: number) => void;
}

const ContactDetailModal: React.FC<ContactDetailModalProps> = ({
  contact,
  isOpen,
  onClose,
  onUpdateContact: _onUpdateContact,
  onAddNote,
  onDeleteNote
}) => {
  const [newNote, setNewNote] = useState('');
  const [noteType, setNoteType] = useState<ContactNote['type']>('NOTE');
  const [isAddingNote, setIsAddingNote] = useState(false);
  if (!isOpen) return null;

  const handleAddNote = async () => {
    if (!newNote.trim()) return;

    const noteRequest: AddNoteRequest = {
      note: newNote,
      type: noteType
    };

    try {
      await onAddNote(contact.id, [noteRequest]);
      setNewNote('');
      setIsAddingNote(false);
    } catch (error) {
      console.error('Failed to add note:', error);
    }
  };

  const handleDeleteNote = async (noteId: number) => {
    if (confirm('Are you sure you want to delete this note?')) {
      try {
        await onDeleteNote(contact.id, noteId);
      } catch (error) {
        console.error('Failed to delete note:', error);
        alert('Failed to delete note. Please try again.');
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getNoteIcon = (type: ContactNote['type']) => {
    switch (type) {
      case 'CALL': return <FaPhone className="text-green-500" />;
      case 'EMAIL': return <FaEnvelope className="text-blue-500" />;
      case 'MEETING': return <FaCalendarAlt className="text-purple-500" />;
      case 'FOLLOW_UP': return <FaClock className="text-orange-500" />;
      default: return <FaStickyNote className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${contact.name} - Contact Details`}
      size="xl"
    >
      {/* Contact Header */}
      <div className="flex items-center space-x-4 mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
          <FaUser className="text-white text-lg" />
        </div>
        <div>
          <h3 className="text-lg font-bold text-gray-900">{contact.name}</h3>
          <p className="text-gray-600">{contact.email}</p>
          <p className="text-gray-600">Contact ID: {contact.id}</p>
        </div>
      </div>

      {/* Content */}
      <div className="max-h-[60vh] overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Contact Information */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Info */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                      <div className="flex items-center space-x-2">
                        <FaUser className="text-gray-400" />
                        <span>{contact.name}</span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                      <div className="flex items-center space-x-2">
                        <FaEnvelope className="text-gray-400" />
                        <a href={`mailto:${contact.email}`} className="text-blue-600 hover:text-blue-800">
                          {contact.email}
                        </a>
                      </div>
                    </div>
                    {contact.phone && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <div className="flex items-center space-x-2">
                          <FaPhone className="text-gray-400" />
                          <a href={`tel:${contact.phone}`} className="text-blue-600 hover:text-blue-800">
                            {contact.phone}
                          </a>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Inquiry Type</label>
                    <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full capitalize">
                      {contactService.getContactTypeDisplayName(contact.type)}
                    </span>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                    <p className="text-gray-900">{contact.subject}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
                    <p className="text-gray-700 bg-gray-50 p-3 rounded-md">{contact.message}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Notes Section */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">Notes & Activity</h3>
                    <Button
                      onClick={() => setIsAddingNote(true)}
                      variant="outline"
                      size="sm"
                      className="flex items-center space-x-1"
                    >
                      <FaPlus />
                      <span>Add Note</span>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {isAddingNote && (
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <div className="space-y-3">
                        <Select
                          value={noteType}
                          onChange={(e) => setNoteType(e.target.value as ContactNote['type'])}
                          options={contactService.getNoteTypeOptions()}
                        />
                        <Textarea
                          value={newNote}
                          onChange={(e) => setNewNote(e.target.value)}
                          placeholder="Add your note here..."
                          rows={3}
                        />
                        <div className="flex space-x-2">
                          <Button onClick={handleAddNote} size="sm">
                            Save Note
                          </Button>
                          <Button 
                            onClick={() => {
                              setIsAddingNote(false);
                              setNewNote('');
                            }}
                            variant="outline" 
                            size="sm"
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="space-y-3">
                    {contact.note.map((note) => (
                      <div key={note.id} className="border rounded-lg p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center space-x-2 mb-2">
                            {getNoteIcon(note.type)}
                            <span className="text-sm font-medium text-gray-900 capitalize">
                              {contactService.getNoteTypeOptions().find(opt => opt.value === note.type)?.label || note.type}
                            </span>
                            <span className="text-xs text-gray-500">
                               {formatDate(note.createdAt)}
                            </span>
                          </div>
                          <button
                            onClick={() => handleDeleteNote(note.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <FaTrash className="text-xs" />
                          </button>
                        </div>
                        <p className="text-gray-700 text-sm">{note.note}</p>
                      </div>
                    ))}
                    
                    {contact.note.length === 0 && (
                      <p className="text-gray-500 text-center py-4">No notes yet</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Status & Priority */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold text-gray-900">Status</h3>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs ${getStatusColor(contact.status)}`}>
                      {contact.status.replace('_', ' ')}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs ${getPriorityColor(contact.priority)}`}>
                      {contact.priority}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Source</label>
                    <span className="text-gray-900">{contact.source.replace('_', ' ')}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Timestamps */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold text-gray-900">Timeline</h3>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Created:</span>
                    <br />
                    <span className="text-gray-600">{formatDate(contact.createdAt)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Last Updated:</span>
                    <br />
                    <span className="text-gray-600">{formatDate(contact.lastUpdate)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
    </Modal>
  );
};

export default ContactDetailModal;
