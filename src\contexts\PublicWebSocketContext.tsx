import React, { createContext, useContext, useEffect, useRef, useState, ReactNode } from 'react';
import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';

// Public WebSocket message types
export interface PublicWebSocketMessage {
  message: string;
  type: 'INFO' | 'ERROR' | 'WARNING';
  status: number;
  event: 'ADD' | 'UPDATE' | 'DELETE';
  field: 'PROJECT';
  data: any[];
}

// Context interface
interface PublicWebSocketContextType {
  isConnected: boolean;
  lastMessage: PublicWebSocketMessage | null;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  subscribe: (callback: (message: PublicWebSocketMessage) => void) => () => void;
  unsubscribe: (callback: (message: PublicWebSocketMessage) => void) => void;
  reconnect: () => void;
}

// Create context
const PublicWebSocketContext = createContext<PublicWebSocketContextType | undefined>(undefined);

// Provider props
interface PublicWebSocketProviderProps {
  children: ReactNode;
  url?: string;
}

// Public WebSocket Provider Component
export const PublicWebSocketProvider: React.FC<PublicWebSocketProviderProps> = ({ 
  children, 
  url = import.meta.env.VITE_API_BASE_URL + '/ws/user' 
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<PublicWebSocketMessage | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  
  const stompClientRef = useRef<Client | null>(null);
  const subscribersRef = useRef<Set<(message: PublicWebSocketMessage) => void>>(new Set());
  const reconnectTimeoutRef = useRef<number | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  // Initialize WebSocket connection
  const connect = () => {
    if (stompClientRef.current?.connected) {
      console.log('🔌 Public WebSocket already connected');
      return;
    }

    setConnectionStatus('connecting');
    console.log('🔌 Connecting to Public WebSocket...');

    try {
      // Ensure global polyfill is available
      if (typeof (window as any).global === 'undefined') {
        (window as any).global = globalThis;
      }

      // Create SockJS socket for public users
      const socket = new SockJS(url);
      
      // Create STOMP client (no authentication needed for public)
      const stompClient = new Client({
        webSocketFactory: () => socket,
        debug: (str) => {
          console.log('🔌 Public STOMP Debug:', str);
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
      });

      // Connection established
      stompClient.onConnect = (frame) => {
        console.log('✅ Public WebSocket Connected:', frame);
        setIsConnected(true);
        setConnectionStatus('connected');
        reconnectAttemptsRef.current = 0;

        // Subscribe to public user topic (no auth headers needed)
        stompClient.subscribe('/topic/user', (message) => {
          try {
            console.log('📩 Raw Public WebSocket message:', message.body);
            const parsedMessage: PublicWebSocketMessage = JSON.parse(message.body);
            console.log('📩 Received Public WebSocket message:', parsedMessage);
            
            setLastMessage(parsedMessage);
            
            // Notify all subscribers
            subscribersRef.current.forEach(callback => {
              try {
                callback(parsedMessage);
              } catch (error) {
                console.error('❌ Error in public subscriber callback:', error);
              }
            });
          } catch (error) {
            console.error('❌ Error parsing public WebSocket message:', error);
          }
        });
      };

      // Connection error
      stompClient.onStompError = (frame) => {
        console.error('❌ Public STOMP Error:', frame.headers['message']);
        console.error('❌ Additional details:', frame.body);
        setIsConnected(false);
        setConnectionStatus('error');
        handleReconnect();
      };

      // Connection closed
      stompClient.onDisconnect = () => {
        console.log('🔌 Public WebSocket Disconnected');
        setIsConnected(false);
        setConnectionStatus('disconnected');
        handleReconnect();
      };

      // WebSocket error
      stompClient.onWebSocketError = (error) => {
        console.error('❌ Public WebSocket Error:', error);
        setConnectionStatus('error');
        handleReconnect();
      };

      stompClientRef.current = stompClient;
      stompClient.activate();

    } catch (error) {
      console.error('❌ Failed to create public WebSocket connection:', error);
      setConnectionStatus('error');
      handleReconnect();
    }
  };

  // Handle reconnection logic
  const handleReconnect = () => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      console.log('❌ Max public reconnection attempts reached');
      setConnectionStatus('error');
      return;
    }

    reconnectAttemptsRef.current++;
    const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
    
    console.log(`🔄 Attempting to reconnect public WebSocket in ${delay}ms (attempt ${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  };

  // Manual reconnect function
  const reconnect = () => {
    reconnectAttemptsRef.current = 0;
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (stompClientRef.current) {
      stompClientRef.current.deactivate();
    }
    
    setTimeout(() => {
      connect();
    }, 1000);
  };

  // Subscribe to messages
  const subscribe = (callback: (message: PublicWebSocketMessage) => void) => {
    subscribersRef.current.add(callback);
    
    // Return unsubscribe function
    return () => {
      subscribersRef.current.delete(callback);
    };
  };

  // Unsubscribe from messages
  const unsubscribe = (callback: (message: PublicWebSocketMessage) => void) => {
    subscribersRef.current.delete(callback);
  };

  // Initialize connection on mount
  useEffect(() => {
    connect();

    // Cleanup on unmount
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      if (stompClientRef.current) {
        console.log('🔌 Disconnecting Public WebSocket...');
        stompClientRef.current.deactivate();
      }
    };
  }, [url]);

  // Context value
  const contextValue: PublicWebSocketContextType = {
    isConnected,
    lastMessage,
    connectionStatus,
    subscribe,
    unsubscribe,
    reconnect,
  };

  return (
    <PublicWebSocketContext.Provider value={contextValue}>
      {children}
    </PublicWebSocketContext.Provider>
  );
};

// Custom hook to use Public WebSocket context
export const usePublicWebSocket = (): PublicWebSocketContextType => {
  const context = useContext(PublicWebSocketContext);
  if (context === undefined) {
    throw new Error('usePublicWebSocket must be used within a PublicWebSocketProvider');
  }
  return context;
};

// Hook for specific field updates (projects only for public)
export const usePublicWebSocketField = (field: PublicWebSocketMessage['field']) => {
  const { subscribe } = usePublicWebSocket();
  const [fieldMessage, setFieldMessage] = useState<PublicWebSocketMessage | null>(null);

  useEffect(() => {
    const unsubscribe = subscribe((message) => {
      if (message.field === field) {
        setFieldMessage(message);
      }
    });

    return unsubscribe;
  }, [field, subscribe]);

  return fieldMessage;
};

export default PublicWebSocketContext;
