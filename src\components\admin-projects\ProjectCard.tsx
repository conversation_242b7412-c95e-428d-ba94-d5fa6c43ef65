import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON> } from '../ui';
import { FaEdit, FaTrash, Fa<PERSON>sers, Fa<PERSON>lock, <PERSON>a<PERSON>ye } from 'react-icons/fa';
import { Project } from '../../types/projects';

interface ProjectCardProps {
  project: Project;
  onView: (project: Project) => void;
  onEdit: (project: Project) => void;
  onDelete: (project: Project) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, onView, onEdit, onDelete }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Open':
        return 'bg-green-100 text-green-800';
      case 'Closed':
        return 'bg-red-100 text-red-800';
      case 'Full':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // const calculateFillPercentage = (current: number, max: number) => {
  //   return max > 0 ? Math.round((current / max) * 100) : 0;
  // };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        {/* Project Header */}
        <div className="mb-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="font-bold text-lg text-gray-900 line-clamp-2">{project.title}</h3>
            <div className="flex items-center space-x-1 ml-2">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                {project.status}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 mb-3">
            <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
              Project ID:  {project.id}
            </span>
          </div>

          <div className="flex items-center space-x-2 mb-3">
            <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
              {project.category}
            </span>
          </div>

          <p className="text-sm text-gray-600 line-clamp-3 mb-3">{project.description}</p>
        </div>

        {/* Project Image */}
        {project.image ? (
          <div className="mb-4">
            <img
              src={project.image}
              alt={project.title}
              className="w-full h-32 object-cover rounded-lg"
            />
          </div>
        ) : (
          <div className="mb-4">
            <div className="w-full h-32 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center rounded-lg">
              <div className="text-center text-gray-500">
                <div className="text-2xl mb-1">📋</div>
                <div className="text-xs">Project</div>
              </div>
            </div>
          </div>
        )}

        {/* Technologies */}
        <div className="mb-4">
          <p className="text-sm font-medium text-gray-700 mb-2">Technologies:</p>
          <div className="flex flex-wrap gap-1">
            {project.technologies.slice(0, 4).map((tech, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-md"
              >
                {tech}
              </span>
            ))}
            {project.technologies.length > 4 && (
              <span className="px-2 py-1 bg-gray-50 text-gray-600 text-xs rounded-md">
                +{project.technologies.length - 4} more
              </span>
            )}
          </div>
        </div>

        {/* Project Stats */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <FaUsers className="text-gray-400" />
              <span className="text-gray-600">Applications</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-900">
                {project.applicationCount}/{project.maxInterns}
              </span>
              {/* <span className="text-xs text-gray-500">
                ({calculateFillPercentage(project.applicationCount, project.maxInterns)}%)
              </span> */}
            </div>
          </div>

          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <FaClock className="text-gray-400" />
              <span className="text-gray-600">Duration</span>
            </div>
            <span className="font-medium text-gray-900">{project.duration}</span>
          </div>
        </div>

        {/* Prerequisites Count */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Prerequisites Questions</span>
            <span className="font-medium text-gray-900">{project.questionCount} questions</span>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            Dynamic questions configured for this project
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          <Button
            onClick={() => onView(project)}
            variant="outline"
            size="sm"
            className="flex-1 flex items-center justify-center space-x-1"
          >
            <FaEye />
            <span>View</span>
          </Button>
          <Button
            onClick={() => onEdit(project)}
            variant="outline"
            size="sm"
            className="flex-1 flex items-center justify-center space-x-1"
          >
            <FaEdit />
            <span>Edit</span>
          </Button>
          <Button
            onClick={() => onDelete(project)}
            variant="outline"
            size="sm"
            className="flex items-center justify-center space-x-1 text-red-600 border-red-300 hover:bg-red-50"
          >
            <FaTrash />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProjectCard;
