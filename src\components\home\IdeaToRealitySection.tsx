import React from 'react';
import { 
  FaLightbulb, 
  FaCode, 
  FaRocket, 
  FaCheckCircle, 
  FaCogs, 
} from 'react-icons/fa';

const IdeaToRealitySection: React.FC = () => {
  const transformationSteps = [
    {
      id: 1,
      icon: <FaLightbulb className="text-2xl" />,
      title: "Share Your Idea",
      description: "Bring your innovative concept to our expert mentors",
      color: "from-yellow-100 to-amber-100",
      iconColor: "text-amber-600",
      borderColor: "border-amber-200"
    },
    {
      id: 2,
      icon: <FaCogs className="text-2xl" />,
      title: "Technical Planning",
      description: "Get professional guidance on architecture and implementation",
      color: "from-blue-100 to-indigo-100",
      iconColor: "text-blue-600",
      borderColor: "border-blue-200"
    },
    {
      id: 3,
      icon: <FaCode className="text-2xl" />,
      title: "Build Together",
      description: "Develop your project with industry-standard tools and practices",
      color: "from-green-100 to-emerald-100",
      iconColor: "text-emerald-600",
      borderColor: "border-emerald-200"
    },
    {
      id: 4,
      icon: <FaRocket className="text-2xl" />,
      title: "Launch & Scale",
      description: "Deploy your solution and showcase it to potential employers",
      color: "from-purple-100 to-violet-100",
      iconColor: "text-purple-600",
      borderColor: "border-purple-200"
    }
  ];


  return (
    <section className="relative py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-indigo-50 via-white to-purple-50 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-indigo-400 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-purple-400 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-cyan-300 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold mb-6">
            <FaLightbulb className="mr-2" />
            Innovation Hub
          </div>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-slate-900 mb-6">
            Transform Your
            <span className="block bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-transparent bg-clip-text">
              Ideas Into Reality
            </span>
          </h2>
          
          <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
            Don't just learn technology - create with it! Bring your innovative ideas to life with expert guidance, 
            industry mentorship, and cutting-edge tools. Turn your vision into a portfolio-worthy project.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-20">
          {/* Left Content - Process Steps */}
          <div>
            <h3 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-8">
              Your Journey from Concept to Creation
            </h3>
            
            <div className="space-y-6">
              {transformationSteps.map((step, index) => (
                <div
                  key={step.id}
                  className="group relative flex items-start space-x-4 p-6 rounded-2xl bg-white border border-slate-200 hover:shadow-lg transition-all duration-300"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Step Number */}
                  <div className="flex-shrink-0">
                    <div className={`w-12 h-12 bg-gradient-to-br ${step.color} ${step.borderColor} border-2 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <div className={step.iconColor}>
                        {step.icon}
                      </div>
                    </div>
                  </div>
                  
                  {/* Step Content */}
                  <div className="flex-1">
                    <h4 className="text-lg font-bold text-slate-900 mb-2 group-hover:text-indigo-700 transition-colors duration-300">
                      {step.title}
                    </h4>
                    <p className="text-slate-600 leading-relaxed">
                      {step.description}
                    </p>
                  </div>

                  {/* Step Number Badge */}
                  <div className="absolute -top-2 -left-2 w-6 h-6 bg-indigo-600 text-white text-xs font-bold rounded-full flex items-center justify-center">
                    {step.id}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content - Visual/Image */}
          <div className="relative">
            <div className="relative bg-gradient-to-br from-indigo-100 to-purple-100 rounded-3xl p-8 border border-indigo-200">
              {/* Placeholder for project showcase image */}
              <div className="aspect-square bg-white rounded-2xl p-8 flex flex-col items-center justify-center border border-slate-200 shadow-inner">
                <div className="w-24 h-24 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 animate-pulse">
                  <FaRocket className="text-white text-3xl" />
                </div>
                <h4 className="text-xl font-bold text-slate-900 mb-3 text-center">Your Project Here</h4>
                <p className="text-slate-600 text-center text-sm leading-relaxed">
                  From mobile apps to AI solutions, web platforms to IoT devices - 
                  bring any tech idea to life with our support
                </p>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg animate-bounce">
                <FaLightbulb className="text-white text-2xl" />
              </div>
              
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center shadow-lg animate-pulse">
                <FaCheckCircle className="text-white text-2xl" />
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl p-8 lg:p-12 text-white">
            <h3 className="text-2xl sm:text-3xl font-bold mb-4">
              Ready to Build Something Amazing?
            </h3>
            <p className="text-indigo-100 text-lg mb-8 max-w-2xl mx-auto">
              Join our innovation program and turn your ideas into industry-ready projects. 
              Get the mentorship, tools, and support you need to succeed.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <button className="bg-white text-indigo-600 px-8 py-4 rounded-xl font-semibold hover:bg-indigo-50 transition-colors duration-300 flex items-center space-x-2">
                <FaRocket />
                <span>Start Your Project</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default IdeaToRealitySection;
