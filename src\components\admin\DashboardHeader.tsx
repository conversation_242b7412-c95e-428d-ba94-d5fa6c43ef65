import React from 'react';
import { FaCalendarAlt } from 'react-icons/fa';

const DashboardHeader: React.FC = () => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-1">Welcome back! Here's what's happening with your internship program.</p>
      </div>
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <FaCalendarAlt />
        <span>{new Date().toLocaleDateString('en-US', { 
          weekday: 'long', 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        })}</span>
      </div>
    </div>
  );
};

export default DashboardHeader;
