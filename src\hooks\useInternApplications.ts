import { useState, useEffect } from 'react';
import internService, { InternApplicant, ApplicationCount, AddApplicantNoteRequest, ProjectNameId } from '../services/internService';
import { useWebSocketField } from '../contexts/WebSocketContext';

interface ApplicationFilters {
  text?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  projectId?: string;
}

interface UseInternApplicationsReturn {
  applications: InternApplicant[];
  applicationCount: ApplicationCount;
  projects: ProjectNameId[];
  loading: boolean;
  error: string | null;
  filters: ApplicationFilters;
  refreshApplications: () => Promise<void>;
  changeApplicationStatus: (id: number, status: InternApplicant['applicationStatus']) => Promise<void>;
  addApplicantNote: (id: number, notes: AddApplicantNoteRequest[]) => Promise<InternApplicant | null>;
  deleteApplicantNote: (noteId: number, applicantId: number) => Promise<InternApplicant | null>;
  deleteApplication: (id: number) => Promise<void>;
  addApplicationAsUser: (id: number) => Promise<void>;
  updateFilters: (newFilters: Partial<ApplicationFilters>) => void;
  applyFilters: () => Promise<void>;
  resetFilters: () => Promise<void>;
}

const useInternApplications = (): UseInternApplicationsReturn => {
  const [applications, setApplications] = useState<InternApplicant[]>([]);
  const [applicationCount, setApplicationCount] = useState<ApplicationCount>({
    rejected: 0,
    reviewing: 0,
    approved: 0,
    pending: 0,
    total: 0
  });
  const [projects, setProjects] = useState<ProjectNameId[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ApplicationFilters>({
    text: '',
    startDate: '',
    endDate: '',
    status: 'ALL',
    projectId: 'ALL'
  });

  const refreshApplications = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await internService.getInternApplications(filters);
      setApplications(response.data.applicantDetails);
      setApplicationCount(response.data.applicationCount);
      setProjects(response.data.projectNameIds || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch intern applications');
      console.error('Error fetching intern applications:', err);
    } finally {
      setLoading(false);
    }
  };

  const changeApplicationStatus = async (id: number, status: InternApplicant['applicationStatus']) => {
    try {
      await internService.changeApplicationStatus(id, status);
      await refreshApplications(); // Refresh the list after status change
    } catch (err: any) {
      setError(err.message || 'Failed to change application status');
      throw err;
    }
  };

  const addApplicantNote = async (id: number, notes: AddApplicantNoteRequest[]): Promise<InternApplicant | null> => {
    try {
      await internService.addApplicantNote(id, notes);
      // Refresh and get the updated data
      const response = await internService.getInternApplications();
      setApplications(response.data.applicantDetails);
      setApplicationCount(response.data.applicationCount);
      // Return the updated application
      return response.data.applicantDetails.find(app => app.id === id) || null;
    } catch (err: any) {
      setError(err.message || 'Failed to add applicant note');
      throw err;
    }
  };

  const deleteApplicantNote = async (noteId: number, applicantId: number): Promise<InternApplicant | null> => {
    try {
      await internService.deleteApplicantNote(noteId, applicantId);
      // Refresh and get the updated data
      const response = await internService.getInternApplications();
      setApplications(response.data.applicantDetails);
      setApplicationCount(response.data.applicationCount);
      // Return the updated application
      return response.data.applicantDetails.find(app => app.id === applicantId) || null;
    } catch (err: any) {
      setError(err.message || 'Failed to delete applicant note');
      throw err;
    }
  };

  const deleteApplication = async (id: number) => {
    try {
      await internService.deleteApplication(id);
      await refreshApplications(); // Refresh the list after deleting application
    } catch (err: any) {
      setError(err.message || 'Failed to delete application');
      throw err;
    }
  };

  const addApplicationAsUser = async (id: number) => {
    try {
      await internService.addApplicationAsUser(id);
      await refreshApplications(); // Refresh the list after adding as user
    } catch (err: any) {
      setError(err.message || 'Failed to add application as user');
      throw err;
    }
  };

  // Filter functions
  const updateFilters = (newFilters: Partial<ApplicationFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const applyFilters = async () => {
    await refreshApplications();
  };

  const resetFilters = async () => {
    // Reset filters to default state
    setFilters({
      text: '',
      startDate: '',
      endDate: '',
      status: 'ALL',
      projectId: 'ALL'
    });
    // Immediately fetch applications without any filters
    try {
      setLoading(true);
      const response = await internService.getInternApplications();

      setApplications(response.data.applicantDetails);
      setApplicationCount(response.data.applicationCount);
      setProjects(response.data.projectNameIds || []);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load applications');
      console.error('Error fetching applications:', err);
    } finally {
      setLoading(false);
    }
  };

  // WebSocket integration for real-time updates
  const applicationMessage = useWebSocketField('APPLICATION');

  useEffect(() => {
    if (applicationMessage) {
      console.log('📩 Application WebSocket update:', applicationMessage);

      switch (applicationMessage.event) {
        case 'ADD':
          // Refresh applications to get the new application
          refreshApplications();
          break;
        case 'UPDATE':
          // Update specific application or refresh all
          if (applicationMessage.data?.applicantDetails) {
            setApplications(applicationMessage.data.applicantDetails);
            if (applicationMessage.data.applicationCount) {
              setApplicationCount(applicationMessage.data.applicationCount);
            }
            if (applicationMessage.data.projectNameIds) {
              setProjects(applicationMessage.data.projectNameIds);
            }
          } else {
            refreshApplications();
          }
          break;
        case 'DELETE':
          // Refresh applications to remove deleted application
          refreshApplications();
          break;
        default:
          console.log('Unknown application event:', applicationMessage.event);
      }
    }
  }, [applicationMessage]);

  // Load applications on mount
  useEffect(() => {
    refreshApplications();
  }, []);

  return {
    applications,
    applicationCount,
    projects,
    loading,
    error,
    filters,
    refreshApplications,
    changeApplicationStatus,
    addApplicantNote,
    deleteApplicantNote,
    deleteApplication,
    addApplicationAsUser,
    updateFilters,
    applyFilters,
    resetFilters,
  };
};

export default useInternApplications;
