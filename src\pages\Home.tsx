import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from '../components/layout';
import { HeroSection, PartnershipSection, TechnologyShowcase } from '../components/home';

const Home: React.FC = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Structured Data for Homepage */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "MSS Internship - Mothercode Software Systems",
          "url": "https://internship.mothercode.com",
          "description": "Premier internship platform for real-world tech experience with industry certification",
          "potentialAction": {
            "@type": "SearchAction",
            "target": "https://internship.mothercode.com/projects?search={search_term_string}",
            "query-input": "required name=search_term_string"
          },
          "mainEntity": {
            "@type": "EducationalOrganization",
            "name": "MSS Internship",
            "description": "Leading internship platform connecting students with real-world tech projects",
            "offers": {
              "@type": "Course",
              "name": "Professional Internship Programs",
              "description": "Industry-focused internship programs with certification",
              "provider": {
                "@type": "Organization",
                "name": "MSS Internship - Mothercode Software Systems"
              }
            }
          }
        })}
      </script>

      <Header />
      <main id="main-content" role="main" aria-label="Main content">
        <HeroSection />
        <TechnologyShowcase />
        <PartnershipSection />
        {/* <IdeaToRealitySection />
        <ProjectsSection />
        <AboutSection /> */}
      </main>
      <Footer />
    </div>
  );
};

export default Home;
