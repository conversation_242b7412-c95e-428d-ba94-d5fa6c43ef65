import React, { useState, useEffect } from 'react';
import { Modal, Button, Input, Select } from '../ui';
import { User } from './UserCard';
import userService from '../../services/userService';

interface UserFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (userData: Partial<User>) => void;
  selectedUser: User | null;
}

const UserFormModal: React.FC<UserFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  selectedUser
}) => {
  const [formData, setFormData] = useState<Partial<User>>({});
  const [skillInput, setSkillInput] = useState('');

  useEffect(() => {
    if (isOpen) {
      if (selectedUser) {
        setFormData(selectedUser);
      } else {
        setFormData({ role: 'MANAGER', status: 'ACTIVE', skills: [] });
      }
    }
  }, [selectedUser, isOpen]);

  const handleSkillAdd = () => {
    if (skillInput.trim() && !formData.skills?.includes(skillInput.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...(prev.skills || []), skillInput.trim()]
      }));
      setSkillInput('');
    }
  };

  const handleSkillRemove = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills?.filter(skill => skill !== skillToRemove) || []
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    setFormData({});
  };

  const handleClose = () => {
    onClose();
    // Don't reset form data here, let the useEffect handle it when the modal reopens
    setSkillInput(''); // Just reset the skill input
  };

  return (
    <Modal
      title={selectedUser ? 'Edit User' : 'Add New User'}
      isOpen={isOpen}
      onClose={handleClose}
      size="lg"
    >
      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
              <Input
                type="text"
                value={formData.name || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <Input
                type="email"
                value={formData.email || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                required
              />
            </div>


            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
              <Select
                value={formData.role || 'MANAGER'}
                onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as any }))}
                options={userService.getRoleOptions()}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <Select
                value={formData.status || 'ACTIVE'}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
                options={userService.getStatusOptions()}
              />
            </div>
          </div>

          {/* Phone and Domain */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
              <Input
                type="tel"
                value={formData.phone || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+1234567890"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Domain</label>
              <Input
                type="text"
                value={formData.domain || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, domain: e.target.value }))}
                placeholder="e.g., Full Stack Developer"
                required
              />
            </div>
          </div>

          {/* Skills - For All Roles */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Skills</h3>

            <div>
              <div className="flex space-x-2 mb-2">
                <input
                  type="text"
                  value={skillInput}
                  onChange={(e) => setSkillInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleSkillAdd())}
                  placeholder="Add a skill"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <Button type="button" onClick={handleSkillAdd} variant="outline" size="sm">
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.skills?.map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    {skill}
                    <button
                      type="button"
                      onClick={() => handleSkillRemove(skill)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Portfolio Links - For All Roles */}
          <div className="border-t pt-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Professional Links</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">GitHub Profile</label>
                <Input
                  type="url"
                  value={formData.githubLink || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, githubLink: e.target.value }))}
                  placeholder="https://github.com/username"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Portfolio URL</label>
                <Input
                  type="url"
                  value={formData.portfolioLink || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, portfolioLink: e.target.value }))}
                  placeholder="https://portfolio.com"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">LinkedIn Profile</label>
                <Input
                  type="url"
                  value={formData.linkedinLink || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, linkedinLink: e.target.value }))}
                  placeholder="https://linkedin.com/in/username"
                />
              </div>
            </div>
          </div>

          {/* Note for new users */}
          {!selectedUser && (
            <div className="border-t pt-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="text-sm font-medium text-blue-800 mb-2">📝 Note about User Creation</h3>
                <p className="text-sm text-blue-700">
                  <strong>Interns</strong> are typically added automatically when applications are accepted with complete academic details.
                  This form is for creating <strong>Managers and Team Leaders</strong> with essential professional information only.
                </p>
              </div>
            </div>
          )}

          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <Button
              type="button"
              onClick={handleClose}
              variant="outline"
            >
              Cancel
            </Button>
            <Button type="submit" variant="gradient" glow>
              {selectedUser ? 'Update User' : 'Create User'}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default UserFormModal;
