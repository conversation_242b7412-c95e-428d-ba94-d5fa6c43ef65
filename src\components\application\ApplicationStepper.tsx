import React from 'react';
import { Card, CardContent } from '../ui';
import { FaQuestionCircle, FaUser, FaShieldAlt, FaCheckCircle, FaEnvelope } from 'react-icons/fa';

interface ApplicationStepperProps {
  currentStepIndex: number;
}

interface Step {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

const ApplicationStepper: React.FC<ApplicationStepperProps> = ({ currentStepIndex }) => {
  const steps: Step[] = [
    {
      id: 'email',
      title: 'Email',
      description: 'Enter your email',
      icon: <FaEnvelope />
    },
    {
      id: 'otp',
      title: 'Verification',
      description: 'Email verification',
      icon: <FaShieldAlt />
    },
    {
      id: 'prerequisites',
      title: 'Prerequisites',
      description: 'Check requirements',
      icon: <FaQuestionCircle />
    },
    {
      id: 'details',
      title: 'Your Details',
      description: 'Personal information',
      icon: <FaUser />
    },
    {
      id: 'success',
      title: 'Complete',
      description: 'Application submitted',
      icon: <FaCheckCircle />
    }
  ];

  return (
    <Card className="mb-6 sm:mb-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
      <CardContent className="p-4 sm:p-6 lg:p-8">
        {/* Mobile Stepper - Custom Implementation */}
        <div className="sm:hidden">
          {/* Current Step Display */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white">
                {steps[currentStepIndex].icon}
              </div>
              <div>
                <h3 className="font-semibold text-slate-900 text-sm">
                  {steps[currentStepIndex].title}
                </h3>
                <p className="text-xs text-slate-600">
                  {steps[currentStepIndex].description}
                </p>
              </div>
            </div>
            <div className="text-xs font-medium text-slate-500">
              {currentStepIndex + 1} of {steps.length}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-slate-200 rounded-full h-2 mb-4">
            <div
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${((currentStepIndex + 1) / steps.length) * 100}%` }}
            />
          </div>

          {/* Step Indicators */}
          <div className="flex justify-between items-center">
            {steps.map((step, index) => (
              <div key={step.id} className="flex flex-col items-center space-y-1">
                <div
                  className={`w-6 h-6 rounded-full flex items-center justify-center text-xs transition-all duration-300 ${
                    index <= currentStepIndex
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                      : 'bg-slate-200 text-slate-400'
                  }`}
                >
                  {index < currentStepIndex ? (
                    <FaCheckCircle className="w-3 h-3" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                <span className={`text-xs font-medium transition-colors duration-300 ${
                  index === currentStepIndex ? 'text-blue-600' : 'text-slate-400'
                }`}>
                  {step.title}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Tablet Stepper - Horizontal with Icons */}
        <div className="hidden sm:block lg:hidden">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex flex-col items-center space-y-2 flex-1">
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                    index <= currentStepIndex
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                      : 'bg-slate-200 text-slate-400'
                  }`}
                >
                  {index < currentStepIndex ? (
                    <FaCheckCircle className="w-5 h-5" />
                  ) : (
                    <div className="text-lg">{step.icon}</div>
                  )}
                </div>
                <div className="text-center">
                  <h4 className={`text-sm font-semibold transition-colors duration-300 ${
                    index === currentStepIndex ? 'text-blue-600' : 'text-slate-600'
                  }`}>
                    {step.title}
                  </h4>
                  <p className="text-xs text-slate-500 mt-1">
                    {step.description}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className="absolute top-6 left-1/2 w-full h-0.5 bg-slate-200 -z-10">
                    <div
                      className={`h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500 ${
                        index < currentStepIndex ? 'w-full' : 'w-0'
                      }`}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Desktop Stepper - Full Featured */}
        <div className="hidden lg:block">
          <div className="relative">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex flex-col items-center space-y-3 flex-1 relative">
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 ${
                      index <= currentStepIndex
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-xl scale-110'
                        : 'bg-slate-200 text-slate-400'
                    }`}
                  >
                    {index < currentStepIndex ? (
                      <FaCheckCircle className="w-6 h-6" />
                    ) : (
                      <div className="text-2xl">{step.icon}</div>
                    )}
                  </div>
                  <div className="text-center">
                    <h4 className={`text-lg font-bold transition-colors duration-300 ${
                      index === currentStepIndex ? 'text-blue-600' : 'text-slate-700'
                    }`}>
                      {step.title}
                    </h4>
                    <p className="text-sm text-slate-500 mt-1">
                      {step.description}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className="absolute top-8 left-1/2 w-full h-1 bg-slate-200 -z-10">
                      <div
                        className={`h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500 ${
                          index < currentStepIndex ? 'w-full' : 'w-0'
                        }`}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ApplicationStepper;
