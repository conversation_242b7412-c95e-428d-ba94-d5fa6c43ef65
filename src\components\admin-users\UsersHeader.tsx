import React from 'react';
import { But<PERSON> } from '../ui';
import { FaPlus } from 'react-icons/fa';

interface UsersHeaderProps {
  onAddUser: () => void;
}

const UsersHeader: React.FC<UsersHeaderProps> = ({ onAddUser }) => {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
        <p className="text-gray-600 mt-1">Manage all users: interns, team leaders, and managers. Interns are added automatically when applications are accepted.</p>
      </div>
      <Button
        onClick={onAddUser}
        variant="gradient"
        size="lg"
        className="flex items-center space-x-2"
        glow
      >
        <FaPlus />
        <span>Add User</span>
      </Button>
    </div>
  );
};

export default UsersHeader;
