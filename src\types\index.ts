// Core data types for the MSS Internship internship application system

export interface Company {
  id: string;
  name: string;
  country: string;
  logo?: string;
  description: string;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  requirements: string[];
  duration: string;
  type: 'frontend' | 'backend' | 'fullstack' | 'mobile' | 'ai/ml' | 'devops';
  technologies: string[];
  mentorName: string;
  openPositions: number;
  applicationDeadline: string;
  isActive: boolean;
}

export interface Question {
  id: string;
  projectId: string;
  question: string;
  type: 'text' | 'multiple-choice' | 'yes-no' | 'rating';
  options?: string[];
  required: boolean;
  order: number;
}

export interface Answer {
  questionId: string;
  answer: string | number;
}

export interface InternApplication {
  id: string;
  projectId: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: string;
    address: string;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
  education: {
    institution: string;
    degree: string;
    fieldOfStudy: string;
    graduationYear: string;
    gpa?: string;
  };
  experience: {
    hasWorkExperience: boolean;
    workExperience?: string;
    projects: string;
    skills: string[];
    programmingLanguages: string[];
    frameworks: string[];
  };
  resume: {
    fileName: string;
    fileSize: number;
    uploadDate: string;
  };
  answers: Answer[];
  submittedAt: string;
  status: 'pending' | 'reviewing' | 'accepted' | 'rejected';
}

export interface ChatMessage {
  id: string;
  type: 'question' | 'answer' | 'system';
  content: string;
  timestamp: string;
  questionId?: string;
}
