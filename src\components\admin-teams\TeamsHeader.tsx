import React from 'react';
import { Button } from '../ui';
import { FaPlus, FaUsers } from 'react-icons/fa';

interface TeamsHeaderProps {
  onAddTeam: () => void;
}

const TeamsHeader: React.FC<TeamsHeaderProps> = ({ onAddTeam }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center space-x-3 mb-4 sm:mb-0">
          <div className="p-2 bg-blue-100 rounded-lg">
            <FaUsers className="text-blue-600 text-xl" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Team Management</h1>
            <p className="text-gray-600">Manage teams, assign members, and track projects</p>
          </div>
        </div>
        
        <div className="flex space-x-3">
          <Button
            onClick={onAddTeam}
            variant="gradient"
            className="flex items-center space-x-2"
          >
            <FaPlus className="text-sm" />
            <span>Add Team</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TeamsHeader;
