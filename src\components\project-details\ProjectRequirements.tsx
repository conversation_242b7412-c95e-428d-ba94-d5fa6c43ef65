import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardContent } from '../ui';
import { FaClipboardList, FaCheck } from 'react-icons/fa';
import { Project } from '../../types/projects';

interface ProjectRequirementsProps {
  project: Project;
}

const ProjectRequirements: React.FC<ProjectRequirementsProps> = ({ project }) => {
  return (
    <Card gradient>
      <CardHeader>
        <h2 className="text-xl lg:text-2xl font-bold text-gray-900 flex items-center">
          <FaClipboardList className="mr-2 lg:mr-3 text-lg lg:text-xl text-gray-600" />
          Requirements
        </h2>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2 lg:space-y-3">
          {project.requirements.map((req, index) => (
            <li key={index} className="flex items-start space-x-2 lg:space-x-3">
              <FaCheck className="text-green-500 mt-0.5 lg:mt-1 text-sm lg:text-base" />
              <span className="text-gray-700 text-sm lg:text-base">{req}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};

export default ProjectRequirements;
