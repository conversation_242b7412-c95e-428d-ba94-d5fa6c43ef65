import React from 'react';
import { Project } from '../../hooks';

interface ProjectsHeroProps {
  projects: Project[];
}

const ProjectsHero: React.FC<ProjectsHeroProps> = ({ projects }) => {
  const openPositions = projects.filter(p => p.status === 'ACTIVE').length;
  const totalProjects = projects.length;

  return (
    <section 
      className="py-20 bg-gradient-to-br from-purple-900 via-indigo-900 to-pink-900 relative overflow-hidden"
      aria-label="Projects page hero section"
    >
      {/* Background Animation */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-pink-500/20 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-5xl md:text-7xl font-black text-white mb-6">
          All <span className="gradient-text-accent">Projects</span>
        </h1>
        <p className="text-xl md:text-2xl text-purple-100 mb-8 max-w-4xl mx-auto">
          Explore our complete collection of industry-relevant internship projects designed to 
          transform you into a <span className="gradient-text-accent font-semibold">professional developer</span>
        </p>
        
        {/* Stats */}
        <div className="flex items-center justify-center space-x-4 text-purple-200">
          <span className="flex items-center space-x-2">
            <span className="w-2 h-2 bg-green-400 rounded-full" aria-hidden="true"></span>
            <span>{openPositions} Open Positions</span>
          </span>
          <span aria-hidden="true">•</span>
          <span>{totalProjects} Total Projects</span>
          <span aria-hidden="true">•</span>
          <span>Multiple Difficulty Levels</span>
        </div>
      </div>
    </section>
  );
};

export default ProjectsHero;
