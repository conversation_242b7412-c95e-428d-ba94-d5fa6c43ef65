import apiService, { ApiResponse } from './api';

// Contact related interfaces
export interface ContactRequest {
  name: string;
  email: string;
  phone: string;
  type: 'GENERAL' | 'INTERNSHIP' | 'PARTNERSHIP' | 'TECHNICAL';
  subject: string;
  message: string;
  status: 'NEW';
  priority: 'HIGH';
  source: 'WEBSITE';
}


// Admin contact interfaces
export interface ContactNote {
  id: number;
  note: string;
  createdAt: string;
  type: 'CALL' | 'MEETING' | 'EMAIL' | 'FOLLOW_UP' | 'NOTE';
}

export interface Contact {
  id: number;
  name: string;
  email: string;
  phone: string;
  status: 'NEW' | 'INPROGRESS' | 'RESOLVED' | 'CLOSED';
  note: ContactNote[];
  subject: string;
  message: string;
  type: 'GENERAL' | 'INTERNSHIP' | 'PARTNERSHIP' | 'TECHNICAL';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  source: 'WEBSITE' | 'REFERAL' | 'PHONE' | 'SOCIAL';
  createdAt: string;
  lastUpdate: string;
}

export interface ContactsResponse {
  message: string;
  type: 'INFO' | 'ERROR';
  data: {
    contactTypeList: {
      newCount: number;
      inProgressCount: number;
      resolvingCount: number;
      closedCount: number;
      totalCount: number;
    };
    contacts: Contact[];
  };
  status: number;
}

export interface UpdateContactRequest {
  name: string;
  email: string;
  phone: string;
  type: 'GENERAL' | 'INTERNSHIP' | 'PARTNERSHIP' | 'TECHNICAL';
  subject: string;
  message: string;
  status: 'NEW' | 'INPROGRESS' | 'RESOLVED' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  source: 'WEBSITE' | 'REFERAL' | 'PHONE' | 'SOCIAL';
}

export interface AddNoteRequest {
  note: string;
  type: 'CALL' | 'MEETING' | 'EMAIL' | 'FOLLOW_UP' | 'NOTE';
}

class ContactService {
  // Create a new contact
  async createContact(contactData: Omit<ContactRequest, 'status' | 'priority' | 'source'>): Promise<ApiResponse> {
    try {
      // Prepare the contact request with required constants
      const contactRequest: ContactRequest = {
        ...contactData,
        status: 'NEW',
        priority: 'HIGH',
        source: 'WEBSITE'
      };

      const response = await apiService.post<ApiResponse>('/contact', contactRequest);
      
      return response;
    } catch (error: any) {
      console.error('Error creating contact:', error);
      console.error('Error response data:', error.response?.data);
      console.error('Error status:', error.response?.status);

      // Handle specific error cases
      if (error.response?.status === 400) {
        const errorMessage = error.response.data?.message || error.response.data?.error || 'Bad request - please check your data';
        throw new Error(errorMessage);
      }
      
      if (error.response?.status === 409) {
        throw new Error(error.response.data?.message || 'Contact already exists');
      }
      
      throw new Error(error.response?.data?.message || 'Failed to create contact');
    }
  }

  // Helper method to validate contact data
  validateContactData(data: Partial<ContactRequest>): string[] {
    const errors: string[] = [];

    if (!data.name?.trim()) {
      errors.push('Name is required');
    }

    if (!data.email?.trim()) {
      errors.push('Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('Please enter a valid email address');
    }

    if (!data.phone?.trim()) {
      errors.push('Phone number is required');
    }

    if (!data.type) {
      errors.push('Contact type is required');
    }

    if (!data.subject?.trim()) {
      errors.push('Subject is required');
    }

    if (!data.message?.trim()) {
      errors.push('Message is required');
    }

    return errors;
  }

  // Helper method to get contact type display names
  getContactTypeDisplayName(type: ContactRequest['type']): string {
    const typeMap = {
      GENERAL: 'General Inquiry',
      INTERNSHIP: 'Internship',
      PARTNERSHIP: 'Partnership',
      TECHNICAL: 'Technical Support'
    };
    
    return typeMap[type] || type;
  }

  // Helper method to get all available contact types
  getContactTypes(): Array<{ value: ContactRequest['type']; label: string }> {
    return [
      { value: 'GENERAL', label: 'General Inquiry' },
      { value: 'INTERNSHIP', label: 'Internship' },
      { value: 'PARTNERSHIP', label: 'Partnership' },
      { value: 'TECHNICAL', label: 'Technical Support' }
    ];
  }

  // Utility method to format date to yyyy-mm-dd
  private formatDateForAPI(dateString: string): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  // Admin methods
  // Get all contacts
  async getContacts(filters?: {
    text?: string;
    status?: string;
    source?: string;
    priority?: string;
    enquiryType?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ContactsResponse> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters?.text && filters.text.trim()) {
        queryParams.append('text', filters.text.trim());
      }

      if (filters?.status && filters.status !== 'ALL') {
        queryParams.append('status', filters.status);
      }

      if (filters?.source && filters.source !== 'ALL') {
        queryParams.append('source', filters.source);
      }

      if (filters?.priority && filters.priority !== 'ALL') {
        queryParams.append('priority', filters.priority);
      }

      if (filters?.enquiryType && filters.enquiryType !== 'ALL') {
        queryParams.append('enquiryType', filters.enquiryType);
      }

      if (filters?.startDate) {
        // Ensure date is in yyyy-mm-dd format
        const formattedStartDate = this.formatDateForAPI(filters.startDate);
        if (formattedStartDate) {
          queryParams.append('startDate', formattedStartDate);
        }
      }

      if (filters?.endDate) {
        // Ensure date is in yyyy-mm-dd format
        const formattedEndDate = this.formatDateForAPI(filters.endDate);
        if (formattedEndDate) {
          queryParams.append('endDate', formattedEndDate);
        }
      }

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/contact?${queryString}` : '/contact';

      const response = await apiService.get<ContactsResponse>(endpoint);
      return response;
    } catch (error: any) {
      console.error('Error fetching contacts:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch contacts');
    }
  }

  // Update contact
  async updateContact(id: number, contactData: UpdateContactRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post<ApiResponse>(`/contact/update/${id}`, contactData);
      return response;
    } catch (error: any) {
      console.error('Error updating contact:', error);
      throw new Error(error.response?.data?.message || 'Failed to update contact');
    }
  }

  // Add note to contact
  async addNote(id: number, notes: AddNoteRequest[]): Promise<ApiResponse> {
    try {
      const response = await apiService.post<ApiResponse>(`/contact/add-notes/${id}`, notes);
      return response;
    } catch (error: any) {
      console.error('Error adding note:', error);
      throw new Error(error.response?.data?.message || 'Failed to add note');
    }
  }

  // Delete contact
  async deleteContact(id: number): Promise<ApiResponse> {
    try {
      const response = await apiService.delete<ApiResponse>(`/contact/delete/${id}`);
      return response;
    } catch (error: any) {
      console.error('Error deleting contact:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete contact');
    }
  }

  // Delete contact note
  async deleteContactNote(contactId: number, noteId: number): Promise<ApiResponse> {
    try {
      const response = await apiService.delete<ApiResponse>('/contact/delete/notes', {
        params: {
          contactId,
          noteId
        }
      });
      return response;
    } catch (error: any) {
      console.error('Error deleting note:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete note');
    }
  }

  // Helper methods for admin
  getStatusOptions(): Array<{ value: Contact['status']; label: string }> {
    return [
      { value: 'NEW', label: 'New' },
      { value: 'INPROGRESS', label: 'In Progress' },
      { value: 'RESOLVED', label: 'Resolved' },
      { value: 'CLOSED', label: 'Closed' }
    ];
  }

  getPriorityOptions(): Array<{ value: Contact['priority']; label: string }> {
    return [
      { value: 'LOW', label: 'Low' },
      { value: 'MEDIUM', label: 'Medium' },
      { value: 'HIGH', label: 'High' },
      { value: 'CRITICAL', label: 'Critical' }
    ];
  }

  getSourceOptions(): Array<{ value: Contact['source']; label: string }> {
    return [
      { value: 'WEBSITE', label: 'Website' },
      { value: 'REFERAL', label: 'Referral' },
      { value: 'PHONE', label: 'Phone' },
      { value: 'SOCIAL', label: 'Social Media' }
    ];
  }

  getNoteTypeOptions(): Array<{ value: ContactNote['type']; label: string }> {
    return [
      { value: 'CALL', label: 'Phone Call' },
      { value: 'MEETING', label: 'Meeting' },
      { value: 'EMAIL', label: 'Email' },
      { value: 'FOLLOW_UP', label: 'Follow Up' },
      { value: 'NOTE', label: 'General Note' }
    ];
  }

  // Helper method to get status color
  getStatusColor(status: Contact['status']): string {
    const colorMap = {
      NEW: 'bg-blue-100 text-blue-800',
      INPROGRESS: 'bg-yellow-100 text-yellow-800',
      RESOLVED: 'bg-green-100 text-green-800',
      CLOSED: 'bg-gray-100 text-gray-800'
    };
    return colorMap[status] || 'bg-gray-100 text-gray-800';
  }

  // Helper method to get priority color
  getPriorityColor(priority: Contact['priority']): string {
    const colorMap = {
      LOW: 'bg-green-100 text-green-800',
      MEDIUM: 'bg-yellow-100 text-yellow-800',
      HIGH: 'bg-orange-100 text-orange-800',
      CRITICAL: 'bg-red-100 text-red-800'
    };
    return colorMap[priority] || 'bg-gray-100 text-gray-800';
  }
}

// Create and export singleton instance
const contactService = new ContactService();
export default contactService;
