import React from 'react';
import { InternApplicant } from '../../services/internService';
import ApplicationCard from './ApplicationCard';

interface ApplicationsListProps {
  applications: InternApplicant[];
  onViewApplication: (application: InternApplicant) => void;
}

const ApplicationsList: React.FC<ApplicationsListProps> = ({
  applications,
  onViewApplication
}) => {
  if (applications.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No applications found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {applications.map((application) => (
        <ApplicationCard
          key={application.id}
          application={application}
          onView={onViewApplication}
        />
      ))}
    </div>
  );
};

export default ApplicationsList;
