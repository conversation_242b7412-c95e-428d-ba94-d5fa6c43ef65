import React from 'react';
import { Card, CardContent } from '../ui';
import { ContactStats } from '../../types/contact';

interface ContactsStatsProps {
  stats: ContactStats;
}

const ContactsStats: React.FC<ContactsStatsProps> = ({ stats }) => {
  const statItems = [
    {
      id: 'total',
      value: stats.totalCount,
      label: 'Total',
      color: 'text-gray-900'
    },
    {
      id: 'new',
      value: stats.newCount,
      label: 'New',
      color: 'text-blue-600'
    },
    {
      id: 'inProgress',
      value: stats.inProgressCount,
      label: 'In Progress',
      color: 'text-yellow-600'
    },
    {
      id: 'resolved',
      value: stats.resolvingCount,
      label: 'Resolving',
      color: 'text-green-600'
    },
    {
      id: 'closed',
      value: stats.closedCount,
      label: 'Closed',
      color: 'text-gray-600'
    }
  ];

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 xl:grid-cols-5 gap-4">
      {statItems.map((item) => (
        <Card key={item.id} className="hover:shadow-lg transition-shadow">
          <CardContent className="p-4 text-center">
            <div className={`text-xl sm:text-2xl font-bold ${item.color}`}>{item.value}</div>
            <div className="text-xs sm:text-sm text-gray-600">{item.label}</div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ContactsStats;
