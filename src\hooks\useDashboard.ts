import { useState, useEffect } from 'react';
import dashboardService, { DashboardData } from '../services/dashboardService';

interface UseDashboardReturn {
  dashboardData: DashboardData | null;
  loading: boolean;
  error: string | null;
  refreshDashboard: () => Promise<void>;
  stats: {
    projectCount: number;
    applicantCount: number;
    activeInternCount: number;
    contactCount: number;
  };
  recentActivity: {
    totalApplications: number;
    pendingCount: number;
    reviewingCount: number;
    acceptedCount: number;
    rejectedCount: number;
    statusBreakdown: Record<string, number>;
  };
}

const useDashboard = (): UseDashboardReturn => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshDashboard = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await dashboardService.getDashboardDetails();
      setDashboardData(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch dashboard data');
      console.error('Error fetching dashboard data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load dashboard data on mount
  useEffect(() => {
    refreshDashboard();
  }, []);

  // Calculate stats from dashboard data
  const stats = {
    projectCount: dashboardData?.projectCount || 0,
    applicantCount: dashboardData?.applicantCount || 0,
    activeInternCount: dashboardData?.activeInternCount || 0,
    contactCount: dashboardData?.contactCount || 0,
  };

  // Calculate recent activity summary
  const recentActivity = dashboardData?.latestApplicants 
    ? dashboardService.getRecentActivitySummary(dashboardData.latestApplicants)
    : {
        totalApplications: 0,
        pendingCount: 0,
        reviewingCount: 0,
        acceptedCount: 0,
        rejectedCount: 0,
        statusBreakdown: {}
      };

  return {
    dashboardData,
    loading,
    error,
    refreshDashboard,
    stats,
    recentActivity,
  };
};

export default useDashboard;
