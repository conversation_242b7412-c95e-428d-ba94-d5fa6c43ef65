import React from 'react';
import { FaEnvelope } from 'react-icons/fa';

interface EmptyStateProps {
  message?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({ 
  message = "No contacts found" 
}) => {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <FaEnvelope className="text-4xl text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{message}</h3>
      <p className="text-gray-500">Contacts will appear here when people submit inquiries through your website.</p>
    </div>
  );
};

export default EmptyState;
