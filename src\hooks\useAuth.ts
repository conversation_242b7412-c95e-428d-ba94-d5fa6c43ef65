import { useState, useEffect } from 'react';
import apiService, { LoginRequest } from '../services/api';

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}



const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: apiService.isAuthenticated(),
    isLoading: true,
    error: null
  });

  // Check for existing session on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = apiService.getToken();

      if (token) {
        try {
          // Validate token with backend
          const isValid = await apiService.validateToken();

          if (isValid) {
            // Create user object from token or fetch user data
            const user: User = {
              id: '1',
              email: '<EMAIL>', // This should come from JWT or API
              name: 'Admin User',
              role: 'admin'
            };

            setAuthState({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } else {
            setAuthState({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: null
            });
          }
        } catch (error) {
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
        }
      } else {
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        });
      }
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const credentials: LoginRequest = { username, password };
      await apiService.adminLogin(credentials);

      // Create user object from response
      const user: User = {
        id: '1',
        email: username,
        name: 'Admin User',
        role: 'admin'
      };

      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed. Please try again.';

      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));

      return { success: false, error: errorMessage };
    }
  };

  const logout = async () => {
    setAuthState(prev => ({ ...prev, isLoading: true }));

    try {
      await apiService.adminLogout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      });
    }
  };

  const isAdmin = () => {
    return authState.user?.role === 'admin';
  };

  return {
    ...authState,
    login,
    logout,
    isAdmin
  };
};

export default useAuth;
