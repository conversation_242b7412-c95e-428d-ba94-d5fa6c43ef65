import React from 'react';
import { Project } from '../../hooks';
import { ApplicationStep } from '../../hooks/useApplicationState';

interface ApplicationHeaderProps {
  project: Project;
  currentStep: ApplicationStep;
}

const ApplicationHeader: React.FC<ApplicationHeaderProps> = ({ currentStep }) => {
  const getStepTitle = () => {
    switch (currentStep) {
      case 'prerequisites':
        return 'Prerequisites Check';
      case 'details':
        return 'Your Information';
      case 'otp':
        return 'Email Verification';
      case 'success':
        return 'Application Complete';
      default:
        return 'Application Process';
    }
  };

  const getStepDescription = () => {
    switch (currentStep) {
      case 'prerequisites':
        return 'Let\'s make sure you meet the requirements for this internship';
      case 'details':
        return 'Tell us about yourself and your background';
      case 'otp':
        return 'We\'ve sent a verification code to your email';
      case 'success':
        return 'Your application has been successfully submitted';
      default:
        return 'Complete your application for this internship';
    }
  };

  return (
    <div className="text-center mb-8">
      <div className="mb-4">
        <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
          {getStepTitle()}
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          {getStepDescription()}
        </p>
      </div>
    </div>
  );
};

export default ApplicationHeader;
