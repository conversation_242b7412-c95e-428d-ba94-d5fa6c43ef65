import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Footer } from '../layout';
import { Button } from '../ui';

interface ApplicationErrorStateProps {
  error: string;
}

const ApplicationErrorState: React.FC<ApplicationErrorStateProps> = ({ error }) => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
      <Header />
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => navigate('/')}>Back to Home</Button>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ApplicationErrorState;
