import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Code, Home, Briefcase } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center space-x-2">
                <Code className="h-8 w-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">MSS Internship</span>
              </Link>
            </div>
            
            <div className="flex items-center space-x-8">
              <Link
                to="/"
                className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive('/') 
                    ? 'text-blue-600 bg-blue-50' 
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                <Home className="h-4 w-4" />
                <span>Home</span>
              </Link>
              
              <Link
                to="/projects"
                className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive('/projects') 
                    ? 'text-blue-600 bg-blue-50' 
                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                }`}
              >
                <Briefcase className="h-4 w-4" />
                <span>Projects</span>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Code className="h-6 w-6 text-blue-600" />
                <span className="text-lg font-bold text-gray-900">MSS Internship</span>
              </div>
              <p className="text-gray-600 text-sm">
                Mothercode Software Systems Internship - Bridging the gap between education and industry 
                with hands-on internship programs.
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-4">Our Partners</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>🇯🇵 Inspeedia, Japan</li>
                <li>🇮🇳 Mother Code, India</li>
                <li>🇺🇸 Ziliconcloud, USA</li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-4">Contact</h3>
              <p className="text-sm text-gray-600">
                Ready to start your journey in tech?<br />
                Apply for our internship programs today!
              </p>
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-gray-200">
            <p className="text-center text-sm text-gray-500">
              © 2025 MSS Internship. All rights reserved. Empowering the next generation of developers.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
