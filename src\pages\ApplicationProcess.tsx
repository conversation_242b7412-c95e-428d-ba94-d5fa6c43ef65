import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Header, Footer } from '../components/layout';
import { useProjects, useApplicationState } from '../hooks';
import {
  ApplicationHeader,
  ApplicationStepper,
  ApplicationStepRenderer,
  ApplicationLoadingState,
  ApplicationErrorState,
  ApplicationNotFoundState
} from '../components/application';

// Types are now exported from ApplicationStepRenderer

const ApplicationProcess: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { project, loading, error, getProjectById } = useProjects();

  useEffect(() => {
    if (id) {
      getProjectById(id);
    }
  }, [id, getProjectById]); 

  const {
    currentStep,
    applicationData,
    currentStepIndex,
    handleStepComplete,
    handleBack
  } = useApplicationState({ projectId: id || '' });

  // Loading state
  if (loading) {
    return <ApplicationLoadingState />;
  }

  // Error state
  if (error) {
    return <ApplicationErrorState error={error} />;
  }


  // Not found state
  if (!project) {
    return <ApplicationNotFoundState />;
  }

  // All step management logic is now handled by useApplicationState hook

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
      <Header />

      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-12">
        {/* Application Header */}
        <ApplicationHeader project={project} currentStep={currentStep} />

        {/* Professional Progress Stepper */}
        <ApplicationStepper currentStepIndex={currentStepIndex} />

        {/* Current Step Content */}
        <ApplicationStepRenderer
          currentStep={currentStep}
          project={project}
          applicationData={applicationData}
          onStepComplete={handleStepComplete}
          onBack={handleBack}
        />
      </main>

      <Footer />
    </div>
  );
};

export default ApplicationProcess;
