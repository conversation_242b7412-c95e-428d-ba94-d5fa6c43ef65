import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, Input } from '../ui';
import { Project } from '../../hooks';

interface StudentDetailsStepProps {
  project: Project;
  initialData: any;
  onComplete: (data: any) => void;
  onBack: () => void;
}

const StudentDetailsStep: React.FC<StudentDetailsStepProps> = ({ 
  project, 
  initialData, 
  onComplete, 
  onBack 
}) => {
  const [formData, setFormData] = useState({
    personalInfo: {
      fullName: initialData.personalInfo?.fullName || '',
      phone: initialData.personalInfo?.phone || '',
    },
    experience: {skills: initialData.experience?.skills || [],
      githubProfile: initialData.experience?.githubProfile || '',
      portfolioUrl: initialData.experience?.portfolioUrl || '',
      linkedinProfile: initialData.experience?.linkedinProfile || ''
    },
    resume: initialData.resume || null
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [skillInput, setSkillInput] = useState('');
  const [dragActive, setDragActive] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Personal Info validation
    if (!formData.personalInfo.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.personalInfo.phone.trim()) newErrors.phone = 'Phone number is required';

    // Resume validation
    if (!formData.resume) {
      newErrors.resume = 'Resume is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (section: string, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [field]: value
      }
    }));

    // Clear error when user starts typing
    const errorKey = field;
    if (errors[errorKey]) {
      setErrors(prev => ({ ...prev, [errorKey]: '' }));
    }
  };

  const handleSkillAdd = () => {
    if (skillInput.trim() && !formData.experience.skills.includes(skillInput.trim())) {
      setFormData(prev => ({
        ...prev,
        experience: {
          ...prev.experience,
          skills: [...prev.experience.skills, skillInput.trim()]
        }
      }));
      setSkillInput('');
    }
  };

  const handleSkillRemove = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      experience: {
        ...prev.experience,
        skills: prev.experience.skills.filter((skill: string) => skill !== skillToRemove)
      }
    }));
  };

  const handleFileUpload = (file: File) => {
    // Validate file type - Only PDF allowed
    if (file.type !== 'application/pdf') {
      setErrors(prev => ({ ...prev, resume: 'Please upload a PDF document only' }));
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, resume: 'File size must be less than 5MB' }));
      return;
    }

    setFormData(prev => ({ ...prev, resume: file }));
    setErrors(prev => ({ ...prev, resume: '' }));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onComplete(formData);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card gradient>
        <CardHeader>
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Your Details</h2>
            <p className="text-gray-600">
              Please provide your information for the <span className="font-semibold text-purple-600">{project.title}</span> internship
            </p>
          </div>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Personal Information */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <span className="mr-2">👤</span>
                Personal Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Full Name *"
                  value={formData.personalInfo.fullName}
                  onChange={(e) => handleInputChange('personalInfo', 'fullName', e.target.value)}
                  error={errors.fullName}
                  placeholder="Enter your full name"
                />
                <Input
                  label="Phone Number *"
                  value={formData.personalInfo.phone}
                  onChange={(e) => handleInputChange('personalInfo', 'phone', e.target.value)}
                  error={errors.phone}
                  placeholder="+91 4442116715"
                />
              </div>
            </div>

            {/* Experience & Skills */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <span className="mr-2">💼</span>
                Experience & Skills
              </h3>
              
              <div className="space-y-4">
                {/* Skills */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Skills</label>
                  <div className="flex space-x-2 mb-2">
                    <input
                      type="text"
                      value={skillInput}
                      onChange={(e) => setSkillInput(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleSkillAdd())}
                      placeholder="Add a skill"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                    <Button type="button" onClick={handleSkillAdd} variant="outline">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.experience.skills.map((skill: string, index: number) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800"
                      >
                        {skill}
                        <button
                          type="button"
                          onClick={() => handleSkillRemove(skill)}
                          className="ml-2 text-purple-600 hover:text-purple-800"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    label="GitHub Profile"
                    value={formData.experience.githubProfile}
                    onChange={(e) => handleInputChange('experience', 'githubProfile', e.target.value)}
                    placeholder="https://github.com/username"
                  />
                  <Input
                    label="Portfolio URL"
                    value={formData.experience.portfolioUrl}
                    onChange={(e) => handleInputChange('experience', 'portfolioUrl', e.target.value)}
                    placeholder="https://yourportfolio.com"
                  />
                  <Input
                    label="LinkedIn Profile"
                    value={formData.experience.linkedinProfile}
                    onChange={(e) => handleInputChange('experience', 'linkedinProfile', e.target.value)}
                    placeholder="https://linkedin.com/in/username"
                  />
                </div>
              </div>
            </div>

            {/* Resume Upload */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <span className="mr-2">📄</span>
                Resume Upload *
              </h3>
              
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${
                  dragActive 
                    ? 'border-purple-500 bg-purple-50' 
                    : errors.resume 
                    ? 'border-red-500 bg-red-50' 
                    : 'border-gray-300 hover:border-purple-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                {formData.resume ? (
                  <div className="space-y-2">
                    <div className="text-green-600 text-4xl">📄</div>
                    <p className="text-green-600 font-medium">{formData.resume.name}</p>
                    <p className="text-sm text-gray-500">
                      {(formData.resume.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setFormData(prev => ({ ...prev, resume: null }))}
                    >
                      Remove
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="text-gray-400 text-4xl">📄</div>
                    <p className="text-gray-600">
                      Drag and drop your resume here, or{' '}
                      <label className="text-purple-600 cursor-pointer hover:text-purple-700">
                        browse
                        <input
                          type="file"
                          className="hidden"
                          accept=".pdf"
                          onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
                        />
                      </label>
                    </p>
                    <p className="text-sm text-gray-500">PDF only (max 5MB)</p>
                  </div>
                )}
              </div>
              {errors.resume && <p className="text-sm text-red-600 mt-1">{errors.resume}</p>}
            </div>

            {/* Submit Buttons */}
            <div className="flex space-x-4 pt-6">
              <Button type="button" variant="outline" onClick={onBack} className="flex-1">
                Back
              </Button>
              <Button type="submit" variant="gradient" className="flex-1" glow>
                Continue to Verification
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentDetailsStep;
