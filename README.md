# MSS Internship - Internship Platform

A comprehensive React TypeScript web application for MSS (Mothercode Software Systems) Internship's internship program. This platform connects students with real-world internship opportunities and provides certification from leading global companies.

## 🌟 Features

### For Students
- **Project Discovery**: Browse available internship projects with detailed descriptions
- **Smart Filtering**: Filter projects by department, difficulty, location, and skills
- **Interactive Application**: Chat-like questionnaire system for project prerequisites
- **Comprehensive Forms**: Complete application forms with resume upload
- **Global Certificates**: Earn certificates from companies in Japan, India, and USA
- **Professional UI**: Modern, responsive design with attractive color palette

### For Companies
- **Multi-Company Support**: Inspeedia Japan, Mothercode India, Ziliconcloud USA
- **Flexible Questions**: Admin-configurable prerequisite questions
- **Application Management**: Track and manage intern applications
- **Certificate Issuance**: Provide verified digital certificates

## 🚀 Technology Stack

- **Frontend**: React 19 with TypeScript
- **Styling**: Tailwind CSS 4.x
- **Routing**: React Router DOM
- **Icons**: Lucide React
- **Build Tool**: Vite
- **Development**: Hot Module Replacement (HMR)

## 📁 Project Structure

```
src/
├── components/
│   ├── layout/          # Header, Footer components
│   ├── ui/              # Reusable UI components
│   └── QuestionChat.tsx # Interactive question system
├── pages/               # Main application pages
├── types/               # TypeScript type definitions
├── data/                # Mock data and configurations
├── context/             # React context for state management
├── utils/               # Helper functions and utilities
└── assets/              # Static assets
```

## 🎨 Design Features

- **Professional Color Palette**: Blue-focused theme with complementary colors
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Interactive Elements**: Hover effects, smooth transitions, and animations
- **Accessibility**: Focus states and keyboard navigation support
- **Modern UI Components**: Cards, badges, buttons with consistent styling
