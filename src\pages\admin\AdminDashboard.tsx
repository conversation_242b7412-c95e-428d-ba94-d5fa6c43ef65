import React from 'react';
import {
  DashboardHeader,
  DashboardStats,
  RecentApplications,
  QuickActions
} from '../../components/admin';
import useDashboard from '../../hooks/useDashboard';

const AdminDashboard: React.FC = () => {
  const { dashboardData, loading, error, stats, recentActivity } = useDashboard();

  if (loading) {
    return (
      <div className="space-y-6">
        <DashboardHeader />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <DashboardHeader />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">Error loading dashboard data: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <DashboardHeader />

      {/* Stats Grid */}
      <DashboardStats stats={stats} />

      {/* Recent Applications and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentApplications
          applications={dashboardData?.latestApplicants || []}
          recentActivity={recentActivity}
        />
        <QuickActions />
      </div>
    </div>
  );
};

export default AdminDashboard;
