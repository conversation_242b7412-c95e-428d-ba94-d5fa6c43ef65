import React from 'react';
import { FaUsers, FaClock, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';

interface ApplicationStats {
  total: number;
  pending: number;
  reviewing: number;
  accepted: number;
  rejected: number;
}

interface ApplicationsStatsProps {
  stats: ApplicationStats;
}

const ApplicationsStats: React.FC<ApplicationsStatsProps> = ({ stats }) => {
  const statItems = [
    {
      id: 'total',
      value: stats.total,
      label: 'Total',
      icon: <FaUsers className="text-blue-600" />,
      bgColor: 'bg-blue-100'
    },
    {
      id: 'pending',
      value: stats.pending,
      label: 'Pending',
      icon: <FaClock className="text-yellow-600" />,
      bgColor: 'bg-yellow-100'
    },
    {
      id: 'reviewing',
      value: stats.reviewing,
      label: 'Reviewing',
      icon: <div className="w-3 h-3 bg-blue-500 rounded-full"></div>,
      bgColor: 'bg-blue-100'
    },
    {
      id: 'accepted',
      value: stats.accepted,
      label: 'Accepted',
      icon: <FaCheckCircle className="text-green-600" />,
      bgColor: 'bg-green-100'
    },
    {
      id: 'rejected',
      value: stats.rejected,
      label: 'Rejected',
      icon: <FaTimesCircle className="text-red-600" />,
      bgColor: 'bg-red-100'
    },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
      {statItems.map((item) => (
        <div key={item.id} className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 ${item.bgColor} rounded-lg flex items-center justify-center`}>
              {item.icon}
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">{item.value}</p>
              <p className="text-sm text-gray-600">{item.label}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ApplicationsStats;
