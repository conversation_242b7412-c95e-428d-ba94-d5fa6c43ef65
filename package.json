{"name": "web-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:test": "vite --mode test", "dev:production": "vite --mode production", "build": "tsc -b && vite build", "build:production": "tsc -b && vite build --mode production", "build:test": "tsc -b && vite build --mode test", "preview": "vite preview", "lint": "eslint .", "test": "echo \"No tests yet\" && exit 0"}, "dependencies": {"@stomp/stompjs": "^7.1.1", "@tailwindcss/vite": "^4.1.11", "@types/react-router-dom": "^5.3.3", "axios": "^1.10.0", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-country-flag": "^3.1.0", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.3", "sockjs-client": "^1.6.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-google-recaptcha": "^2.1.9", "@types/sockjs-client": "^1.5.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}