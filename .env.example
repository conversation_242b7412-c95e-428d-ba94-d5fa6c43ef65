# API Configuration
VITE_API_BASE_URL=http://192.168.29.12:8080

# Environment Mode
# Values: development | test | production
# - development: Shows dummy credentials, dev tools enabled, hot reload
# - test: Shows dummy credentials, optimized for testing, no hot reload
# - production: Hides dummy credentials, production optimized, minified
VITE_NODE_ENV=development

# Demo Credentials (Only visible in development/test modes)
# These credentials will ONLY be displayed when VITE_NODE_ENV is 'development' or 'test'
# In production mode, these will be completely hidden for security
# Replace with your actual demo/test credentials
VITE_DEMO_USERNAME=your_demo_username
VITE_DEMO_PASSWORD=your_demo_password

# reCAPTCHA Configuration (Only active in production mode)
# reCAPTCHA will only be enabled when VITE_NODE_ENV is 'production'
# In development and test modes, captcha verification is bypassed
VITE_RECAPTCHA_SITE_KEY=*****************************************
VITE_RECAPTCHA_SECRET_KEY=****************************************