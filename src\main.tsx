// Polyfill for global variable (required by SockJS/STOMP)
if (typeof (window as any).global === 'undefined') {
  (window as any).global = globalThis;
}

// import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON>rowserRouter } from 'react-router-dom'
import './index.css'
import App from './App.tsx'

createRoot(document.getElementById('root')!).render(
    <BrowserRouter>
      <App />
    </BrowserRouter>
)
