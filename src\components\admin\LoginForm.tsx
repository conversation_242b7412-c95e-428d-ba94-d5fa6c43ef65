import React, { useState } from 'react';
import { Button, Input, ReCaptcha } from '../ui';
import { FaEye, FaEyeSlash, FaEnvelope, FaLock } from 'react-icons/fa';
import { useCaptcha } from '../../hooks';

interface LoginFormProps {
  onSubmit: (username: string, password: string, captchaToken?: string) => Promise<void>;
  isLoading: boolean;
  error: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit, isLoading, error }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);

  // Captcha integration
  const {
    captchaToken,
    captchaError,
    isVerifying,
    isCaptchaEnabled,
    captchaRef,
    handleCaptchaVerify,
    handleCaptchaError,
    handleCaptchaExpired
  } = useCaptcha();

  // Get environment variables
  const nodeEnv = import.meta.env.VITE_NODE_ENV;
  const isDevelopmentOrTest = nodeEnv === 'development' || nodeEnv === 'test';
  const demoUsername = import.meta.env.VITE_DEMO_USERNAME;
  const demoPassword = import.meta.env.VITE_DEMO_PASSWORD;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate captcha in production mode
    if (isCaptchaEnabled && !captchaToken) {
      return; // Don't submit if captcha is required but not completed
    }

    await onSubmit(formData.username, formData.password);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const fillDemoCredentials = () => {
    if (demoUsername && demoPassword) {
      setFormData({
        username: demoUsername,
        password: demoPassword
      });
    }
  };


  return (
    <>
      {/* Demo Credentials - Development/Test Only */}
      {isDevelopmentOrTest && demoUsername && demoPassword && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-sm text-blue-800 mb-2 font-medium">
            Demo Credentials ({nodeEnv === 'test' ? 'Test' : 'Dev'} Mode):
          </p>
          <p className="text-xs text-blue-600 mb-1">Username: {demoUsername}</p>
          <p className="text-xs text-blue-600 mb-3">Password: {demoPassword}</p>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={fillDemoCredentials}
            className="w-full text-blue-600 border-blue-300 hover:bg-blue-100"
          >
            Fill Demo Credentials
          </Button>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Login Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Username Field */}
        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
            Username
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaEnvelope className="text-gray-400" />
            </div>
            <Input
              id="username"
              name="username"
              type="text"
              required
              value={formData.username}
              onChange={handleChange}
              className="pl-10"
              placeholder="Enter your username"
              disabled={isLoading}
            />
          </div>
        </div>

        {/* Password Field */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
            Password
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaLock className="text-gray-400" />
            </div>
            <Input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              required
              value={formData.password}
              onChange={handleChange}
              className="pl-10 pr-10"
              placeholder="Enter your password"
              disabled={isLoading}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <FaEyeSlash className="text-gray-400 hover:text-gray-600" />
              ) : (
                <FaEye className="text-gray-400 hover:text-gray-600" />
              )}
            </button>
          </div>
        </div>

        {/* reCAPTCHA */}
        <div>
          <ReCaptcha
            ref={captchaRef}
            onVerify={handleCaptchaVerify}
            onError={handleCaptchaError}
            onExpired={handleCaptchaExpired}
            size="normal"
            theme="light"
          />
          {captchaError && (
            <p className="text-sm text-red-600 mt-1">{captchaError}</p>
          )}
          {isCaptchaEnabled && !captchaToken && (
            <p className="text-sm text-red-600 mt-1">Please complete the captcha verification</p>
          )}
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="gradient"
          size="lg"
          className="w-full"
          disabled={isLoading || isVerifying || (isCaptchaEnabled && !captchaToken)}
          glow
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Signing In...</span>
            </div>
          ) : (
            'Sign In'
          )}
        </Button>
      </form>
    </>
  );
};

export default LoginForm;
