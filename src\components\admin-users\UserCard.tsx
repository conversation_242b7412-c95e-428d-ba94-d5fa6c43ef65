import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON> } from '../ui';
import { FaEdit, FaTrash, Fa<PERSON><PERSON><PERSON>, FaU<PERSON><PERSON>ie, FaUserShield } from 'react-icons/fa';

export interface User {
  id: number;
  name: string | null;
  email: string;
  role: 'INTERN' | 'MANAGER' | 'TL' | 'ADMIN';
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | null;
  phone: string | null;
  domain: string | null;
  skills: string[] | null;
  githubLink: string | null;
  portfolioLink: string | null;
  linkedinLink: string | null;
}

interface UserCardProps {
  user: User;
  onEdit: (user: User) => void;
  onDelete: (userId: number) => void;
}

const UserCard: React.FC<UserCardProps> = ({ user, onEdit, onDelete }) => {
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'INTERN':
        return <FaUsers className="text-purple-600" />;
      case 'TL':
        return <FaUserTie className="text-indigo-600" />;
      case 'MANAGER':
        return <FaUserShield className="text-emerald-600" />;
      case 'ADMIN':
        return <FaUserShield className="text-blue-600" />;
      default:
        return <FaUsers className="text-gray-600" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'INTERN':
        return 'bg-purple-100 text-purple-800';
      case 'TL':
        return 'bg-indigo-100 text-indigo-800';
      case 'MANAGER':
        return 'bg-emerald-100 text-emerald-800';
      case 'ADMIN':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'INACTIVE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        {/* User Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
              {user.name ? user.name.split(' ').map(n => n[0]).join('').toUpperCase() : user.email[0].toUpperCase()}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{user.name || 'No Name'}</h3>
              <p className="text-sm text-gray-600">{user.email}</p>
              {user.domain && (
                <p className="text-xs text-gray-500">{user.domain}</p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {getRoleIcon(user.role)}
          </div>
        </div>

        {/* Role and Status */}
        <div className="flex items-center space-x-2 mb-4">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
            {user.role === 'TL' ? 'Team Leader' : user.role.charAt(0) + user.role.slice(1).toLowerCase()}
          </span>
          {user.status && (
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
              {user.status.charAt(0).toUpperCase() + user.status.slice(1).toLowerCase()}
            </span>
          )}
        </div>

        {/* Skills */}
        {user.skills && user.skills.length > 0 && (
          <div className="mb-4">
            <p className="text-sm font-medium text-gray-700 mb-2">Skills:</p>
            <div className="flex flex-wrap gap-1">
              {user.skills.slice(0, 3).map((skill, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-md"
                >
                  {skill}
                </span>
              ))}
              {user.skills.length > 3 && (
                <span className="px-2 py-1 bg-gray-50 text-gray-600 text-xs rounded-md">
                  +{user.skills.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center space-x-2 pt-4 border-t border-gray-200">
          <Button
            onClick={() => onEdit(user)}
            variant="outline"
            size="sm"
            className="flex-1 flex items-center justify-center space-x-1"
          >
            <FaEdit />
            <span>Edit</span>
          </Button>
          <Button
            onClick={() => onDelete(user.id)}
            variant="outline"
            size="sm"
            className="flex items-center justify-center space-x-1 text-red-600 border-red-300 hover:bg-red-50"
          >
            <FaTrash />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default UserCard;
