import React, { useState } from 'react';
import useInternApplications from '../../hooks/useInternApplications';
import { InternApplicant } from '../../services/internService';
import {
  ApplicationViewModal,
  ApplicationsHeader,
  ApplicationsStats,
  ApplicationsFilter,
  ApplicationsList
} from '../../components/admin-applications';
import { LoadingState, ErrorState } from '../../components/admin-projects';

const AdminApplications: React.FC = () => {
  const {
    applications,
    applicationCount,
    projects,
    loading,
    error,
    filters,
    refreshApplications,
    changeApplicationStatus,
    addApplicantNote,
    deleteApplicantNote,
    deleteApplication,
    addApplicationAsUser,
    updateFilters,
    applyFilters,
    resetFilters
  } = useInternApplications();
  
  // Modal states
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<InternApplicant | null>(null);


  const handleViewApplication = (application: InternApplicant) => {
    setSelectedApplication(application);
    setIsViewModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsViewModalOpen(false);
    setSelectedApplication(null);
  };

  const handleStatusUpdate = async (applicationId: number, status: InternApplicant['applicationStatus'], notes?: string) => {
    try {
      await changeApplicationStatus(applicationId, status);
      if (notes) {
        await addApplicantNote(applicationId, [{ activityType: 'NOTE', text: notes }]);
      }
      // Refresh applications to get updated data
      await refreshApplications();
      // Update the selected application with fresh data
      if (selectedApplication && selectedApplication.id === applicationId) {
        const updatedApplication = applications.find(app => app.id === applicationId);
        if (updatedApplication) {
          setSelectedApplication(updatedApplication);
        }
      }
    } catch (error) {
      console.error('Failed to update application status:', error);
    }
  };

  const handleAddNote = async (applicationId: number, notes: any[]) => {
    try {
      const updatedApplication = await addApplicantNote(applicationId, notes);
      // Update the selected application with fresh data
      if (selectedApplication && selectedApplication.id === applicationId && updatedApplication) {
        setSelectedApplication(updatedApplication);
      }
    } catch (error) {
      console.error('Failed to add note:', error);
      throw error;
    }
  };

  const handleDeleteNote = async (noteId: number, applicantId: number) => {
    try {
      const updatedApplication = await deleteApplicantNote(noteId, applicantId);
      // Update the selected application with fresh data
      if (selectedApplication && selectedApplication.id === applicantId && updatedApplication) {
        setSelectedApplication(updatedApplication);
      }
    } catch (error) {
      console.error('Failed to delete note:', error);
      throw error;
    }
  };

  const handleDeleteApplication = async (applicationId: number) => {
    try {
      const confirmed = window.confirm('Are you sure you want to delete this application? This action cannot be undone.');
      if (confirmed) {
        await deleteApplication(applicationId);
        handleCloseModal(); // Close modal after deletion
      }
    } catch (error) {
      console.error('Failed to delete application:', error);
      alert('Failed to delete application. Please try again.');
    }
  };

  const handleAddAsIntern = async (applicationId: number) => {
    try {
      const confirmed = window.confirm('Are you sure you want to add this applicant as an intern?');
      if (confirmed) {
        await addApplicationAsUser(applicationId);
        handleCloseModal(); // Close modal after adding as intern
      }
    } catch (error) {
      console.error('Failed to add as intern:', error);
      alert('Failed to add as intern. Please try again.');
    }
  };



  if (loading) {
    return <LoadingState message="Loading applications..." />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={() => window.location.reload()} />;
  }

  // Create stats from applicationCount
  const stats = {
    total: applicationCount.total,
    pending: applicationCount.pending,
    reviewing: applicationCount.reviewing,
    accepted: applicationCount.approved,
    rejected: applicationCount.rejected
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <ApplicationsHeader />

      {/* Stats Cards */}
      <ApplicationsStats stats={stats} />

      {/* Filter Component */}
      <ApplicationsFilter
        filters={filters}
        projects={projects}
        onFiltersChange={updateFilters}
        onApplyFilters={applyFilters}
        onResetFilters={resetFilters}
        loading={loading}
      />

      {/* Applications List */}
      <ApplicationsList
        applications={applications}
        onViewApplication={handleViewApplication}
      />

      {/* Application View Modal */}
      <ApplicationViewModal
        isOpen={isViewModalOpen}
        onClose={handleCloseModal}
        onUpdateStatus={handleStatusUpdate}
        application={selectedApplication}
        onAddNote={handleAddNote}
        onDeleteNote={handleDeleteNote}
        onDeleteApplication={handleDeleteApplication}
        onAddAsIntern={handleAddAsIntern}
      />
    </div>
  );
};

export default AdminApplications;
