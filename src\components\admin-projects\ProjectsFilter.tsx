import React, { useRef } from 'react';
import { FaSearch, FaTimes, FaFilter } from 'react-icons/fa';

interface ProjectFilters {
  status?: string;
  text?: string;
}

interface ProjectsFilterProps {
  filters: ProjectFilters;
  onFiltersChange: (filters: Partial<ProjectFilters>) => void;
  onApplyFilters: () => void;
  onResetFilters: () => Promise<void>;
  loading?: boolean;
}

const ProjectsFilter: React.FC<ProjectsFilterProps> = ({
  filters,
  onFiltersChange,
  onApplyFilters,
  onResetFilters,
  loading = false
}) => {
  const textInputRef = useRef<HTMLInputElement>(null);
  const [clearLoading, setClearLoading] = React.useState(false);

  // Handle text input change (no automatic filtering)
  const handleTextChange = (value: string) => {
    onFiltersChange({ text: value });
  };

  // Handle status dropdown change (no automatic filtering)
  const handleStatusChange = (status: string) => {
    onFiltersChange({ status });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onApplyFilters();
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onApplyFilters();
    }
  };

  // Handle clear filters with loading state
  const handleClearFilters = async () => {
    setClearLoading(true);
    try {
      await onResetFilters();
    } finally {
      setClearLoading(false);
    }
  };

  // Clear text filter
  const clearTextFilter = () => {
    onFiltersChange({ text: '' });
    if (textInputRef.current) {
      textInputRef.current.focus();
    }
  };

  // Check if any filters are active
  const hasActiveFilters = filters.status !== 'ALL' || (filters.text && filters.text.trim() !== '');

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center mb-4">
        <div className="flex items-center space-x-2">
          <FaFilter className="text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-900">Filter Projects</h3>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 items-end">
        {/* Text Search Input */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Search by Title, Category, or Technology
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="h-4 w-4 text-gray-400" />
            </div>
            <input
              ref={textInputRef}
              type="text"
              value={filters.text || ''}
              onChange={(e) => handleTextChange(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Start typing to search..."
              className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
              disabled={loading}
            />
            {filters.text && (
              <button
                onClick={clearTextFilter}
                className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 transition-colors duration-200"
                disabled={loading}
              >
                <FaTimes className="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>
        </div>

        {/* Status Dropdown */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Filter by Status
          </label>
          <select
            value={filters.status || 'ALL'}
            onChange={(e) => handleStatusChange(e.target.value)}
            className="block w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 disabled:bg-gray-50 disabled:cursor-not-allowed"
            disabled={loading}
          >
            <option value="ALL">All Projects</option>
            <option value="ACTIVE">Active Projects</option>
            <option value="INACTIVE">Inactive Projects</option>
            <option value="CLOSED">Closed Projects</option>
          </select>
        </div>

        {/* Filter Button */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Actions
          </label>
          <div className="flex space-x-2">
            <button
              type="submit"
              disabled={loading}
              className="flex-1 inline-flex items-center justify-center px-4 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                  Filtering...
                </>
              ) : (
                <>
                  <FaSearch className="mr-2" />
                  Apply
                </>
              )}
            </button>

            {hasActiveFilters && (
              <button
                type="button"
                onClick={handleClearFilters}
                disabled={loading || clearLoading}
                className="inline-flex items-center justify-center px-4 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                {clearLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    Clearing...
                  </>
                ) : (
                  <>
                    <FaTimes className="mr-2" />
                    Clear Filter
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
      </form>

      {/* Loading Indicator */}
      {loading && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
            <span>Filtering projects...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectsFilter;
