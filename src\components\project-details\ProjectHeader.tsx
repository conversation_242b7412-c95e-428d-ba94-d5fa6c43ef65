import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../ui';
import { FaArrowLeft, FaClock, FaFolder } from 'react-icons/fa';
import { Project } from '../../hooks';

interface ProjectHeaderProps {
  project: Project;
}

const ProjectHeader: React.FC<ProjectHeaderProps> = ({ 
  project
}) => {
  const navigate = useNavigate();

  return (
    <>
      {/* Back Button */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => navigate('/')}
          className="flex items-center space-x-2"
        >
          <FaArrowLeft />
          <span>Back to Projects</span>
        </Button>
      </div>

      {/* Project Header */}
      <div className="mb-8 sm:mb-12">
        {/* Mobile Layout */}
        <div className="block lg:hidden">
          {/* Project Image - Mobile */}
          <div className="relative mb-6 rounded-2xl overflow-hidden shadow-2xl">
            {project.image ? (
              <img
                src={project.image}
                alt={`${project.title} - ${project.category} internship project`}
                className="w-full h-48 sm:h-64 object-cover"
                loading="lazy"
              />
            ) : (
              <div className="w-full h-48 sm:h-64 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <div className="text-4xl mb-2">📋</div>
                  <div className="text-sm">Project Image</div>
                </div>
              </div>
            )}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          </div>

          {/* Project Title and Info - Mobile */}
          <div className="px-2">
            <h1 className="text-2xl sm:text-3xl font-black text-gray-900 mb-4 leading-tight">
              {project.title}
            </h1>
            <div className="flex flex-wrap items-center gap-3 mb-4">
              <div className="flex items-center space-x-2 text-purple-600 bg-purple-50 px-3 py-1 rounded-full">
                <FaClock className="text-sm" />
                <span className="font-medium text-sm">{project.duration}</span>
              </div>
              <div className="flex items-center space-x-2 text-indigo-600 bg-indigo-50 px-3 py-1 rounded-full">
                <FaFolder className="text-sm" />
                <span className="font-medium text-sm">{project.category}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:block">
          <div className="flex items-start space-x-8 mb-6">
            {/* Project Image - Desktop */}
            <div className="flex-shrink-0">
              <div className="relative w-32 h-32 xl:w-40 xl:h-40 rounded-2xl overflow-hidden shadow-2xl">
                {project.image ? (
                  <img
                    src={project.image}
                    alt={`${project.title} - ${project.category} internship project`}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <div className="text-2xl mb-1">📋</div>
                      <div className="text-xs">Project</div>
                    </div>
                  </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />
              </div>
            </div>

            {/* Project Info - Desktop */}
            <div className="flex-1 min-w-0">
              <h1 className="text-4xl xl:text-5xl font-black text-gray-900 mb-4 leading-tight">
                {project.title}
              </h1>
              <div className="flex flex-wrap items-center gap-4">
     
                <div className="flex items-center space-x-2 text-purple-600">
                  <FaClock />
                  <span className="font-medium">{project.duration}</span>
                </div>
                <div className="flex items-center space-x-2 text-indigo-600">
                  <FaFolder />
                  <span className="font-medium">{project.category}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProjectHeader;
