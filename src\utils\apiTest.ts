import apiService from '../services/api';

// Test function to verify API connection
export const testApiConnection = async () => {
  console.log('🔧 Testing API Connection...');
  console.log('Base URL:', import.meta.env.VITE_API_BASE_URL);
  
  try {
    // Test login with demo credentials
    const credentials = {
      username: '<EMAIL>',
      password: '11110000'
    };
    
    console.log('📤 Sending login request with credentials:', credentials);
    
    const response = await apiService.adminLogin(credentials);
    console.log('✅ Login successful:', response);
    
    // Test token validation
    console.log('🔍 Testing token validation...');
    const isValid = await apiService.validateToken();
    console.log('Token validation result:', isValid);
    
    return { success: true, response };
  } catch (error) {
    console.error('❌ API Test failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Debug function to check headers
export const debugApiHeaders = () => {
  const token = apiService.getToken();
  console.log('🔍 Current token:', token);
  console.log('🔍 Is authenticated:', apiService.isAuthenticated());
  
  // Create a test request to see headers
  const testConfig = {
    headers: {} as any
  };
  
  if (token) {
    testConfig.headers.Bearer = token;
  }
  
  console.log('🔍 Headers that would be sent:', testConfig.headers);
};
