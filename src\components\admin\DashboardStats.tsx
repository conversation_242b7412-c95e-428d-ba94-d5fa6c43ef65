import React from 'react';
import { Card, CardContent } from '../ui';
import {
  FaUsers,
  FaProjectDiagram,
  FaClipboardList,
  FaEnvelope
} from 'react-icons/fa';
import { HiTrendingUp } from "react-icons/hi";

interface StatItem {
  id: string;
  title: string;
  value: string;
  change: string;
  icon: React.ReactNode;
  color: string;
  trend: string;
}

interface DashboardStatsProps {
  stats: {
    projectCount: number;
    applicantCount: number;
    activeInternCount: number;
    contactCount: number;
  };
}

const DashboardStats: React.FC<DashboardStatsProps> = ({ stats }) => {
  const statItems: StatItem[] = [
    {
      id: 'total-projects',
      title: 'Total Projects',
      value: stats.projectCount.toString(),
      change: 'Live data',
      icon: <FaProjectDiagram />,
      color: 'bg-blue-500',
      trend: 'up'
    },
    {
      id: 'total-applications',
      title: 'Total Applications',
      value: stats.applicantCount.toString(),
      change: 'Live data',
      icon: <FaClipboardList />,
      color: 'bg-green-500',
      trend: 'up'
    },
    {
      id: 'active-interns',
      title: 'Active Interns',
      value: stats.activeInternCount.toString(),
      change: 'Live data',
      icon: <FaUsers />,
      color: 'bg-purple-500',
      trend: 'up'
    },
    {
      id: 'contact-inquiries',
      title: 'Contact Inquiries',
      value: stats.contactCount.toString(),
      change: 'Live data',
      icon: <FaEnvelope />,
      color: 'bg-orange-500',
      trend: 'up'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((stat) => (
        <Card key={stat.id} className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                <div className="flex items-center mt-2">
                  <HiTrendingUp className="text-green-500 text-sm mr-1" />
                  <span className="text-sm text-green-600">{stat.change}</span>
                </div>
              </div>
              <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center text-white`}>
                {stat.icon}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default DashboardStats;
