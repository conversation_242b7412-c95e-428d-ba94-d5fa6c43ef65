import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';

// API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  message: string;
  type: 'INFO' | 'ERROR';
  status: number;
}


export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  type: 'INFO' | 'ERROR'
  data: {
    jwt: string;
  };
  status: number;
}

// Get base URL from environment variables
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

class ApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Load token from localStorage on initialization
    this.loadToken();

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        if (this.token) {
          // Backend expects token in "Bearer" header, not "Authorization"
          config.headers.Bearer = this.token;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error: AxiosError) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          this.clearToken();
          // Redirect to login page
          window.location.href = '/admin/login';
        }
        return Promise.reject(error);
      }
    );

    
  }

  // Token management
  private loadToken(): void {
    this.token = localStorage.getItem('admin_token');
  }

  private saveToken(token: string): void {
    this.token = token;
    localStorage.setItem('admin_token', token);
  }

  private clearToken(): void {
    this.token = null;
    localStorage.removeItem('admin_token');
  }

  // Check if user is authenticated
  public isAuthenticated(): boolean {
    return !!this.token;
  }

  // Get current token
  public getToken(): string | null {
    return this.token;
  }

  // Admin Authentication
  public async adminLogin(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // Use the correct endpoint path
      const response = await this.api.post<LoginResponse>('/auth/admin/login', credentials);

      if (response.data.data?.jwt) {
        this.saveToken(response.data.data.jwt);
      }

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        // Extract error message from response
        const errorMessage = error.response.data?.message || 'Login failed';
        console.error('Login error:', errorMessage);
        throw new Error(errorMessage);
      }
      console.error('Network error during login');
      throw new Error('Network error occurred');
    }
  }

  // Admin Logout
  public async adminLogout(): Promise<void> {
    try {
      // Call logout endpoint if available
      // await this.api.post('/auth/admin/logout');
      this.clearToken();
    } catch (error) {
      // Clear token even if logout request fails
      this.clearToken();
    }
  }

  // Validate JWT Token
  public async validateToken(): Promise<boolean> {
    if (!this.token) {
      return false;
    }

    try {
      // Call the backend validate endpoint
      const response = await this.api.get('/admin/validate');
      // If we get a successful response with user data, token is valid
      return response.data && response.status === 200;
    } catch (error) {
      this.clearToken();
      return false;
    }
  }

  // Generic API methods
  public async get<T>(url: string): Promise<T> {
    const response = await this.api.get<T>(url);
    return response.data;
  }

  public async post<T>(url: string, data?: any, config?: any): Promise<T> {
    const response = await this.api.post<T>(url, data, config);
    return response.data;
  }

  public async postFormData<T>(url: string, formData: FormData): Promise<T> {
    const response = await this.api.post<T>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  public async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.put<T>(url, data);
    return response.data;
  }

  public async delete<T>(url: string, config?: any): Promise<T> {
    const response = await this.api.delete<T>(url, config);
    return response.data;
  }

  public getBaseUrl(): string {
    return this.api.defaults.baseURL || '';
  }

  // Get API instance for custom requests
  public getApiInstance(): AxiosInstance {
    return this.api;
  }
}

// Create and export singleton instance
const apiService = new ApiService();
export default apiService;