import React from 'react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { FaWifi, FaExclamationTriangle, FaSpinner } from 'react-icons/fa';

const WebSocketStatus: React.FC = () => {
  const { connectionStatus, reconnect } = useWebSocket();

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <FaWifi className="text-green-500" />;
      case 'connecting':
        return <FaSpinner className="text-yellow-500 animate-spin" />;
      case 'error':
      case 'disconnected':
        return <FaExclamationTriangle className="text-red-500" />;
      default:
        return <FaWifi className="text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Connection Error';
      case 'disconnected':
        return 'Disconnected';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'connecting':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error':
      case 'disconnected':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor()}`}>
      {getStatusIcon()}
      <span className="ml-2">{getStatusText()}</span>
      {(connectionStatus === 'error' || connectionStatus === 'disconnected') && (
        <button
          onClick={reconnect}
          className="ml-2 text-xs underline hover:no-underline"
        >
          Retry
        </button>
      )}
    </div>
  );
};

export default WebSocketStatus;
