import React from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, Badge } from '../ui';
import { Project } from '../../hooks';
import { ApplicationData } from '../../hooks/useApplicationState';

interface SuccessStepProps {
  project: Project;
  applicationData: ApplicationData;
  onComplete: (stepData?: any) => void;
}

const SuccessStep: React.FC<SuccessStepProps> = ({ project, applicationData, onComplete }) => {
  // Use the real application ID from the API response
  const applicationId = applicationData.applicationId || 'Processing...';

  return (
    <div className="max-w-2xl mx-auto">
      <Card gradient>
        <CardContent className="text-center py-12">
          {/* Success Animation */}
          <div className="mb-8">
            <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
              <span className="text-white text-4xl">✓</span>
            </div>
            <div className="space-y-2">
              <h2 className="text-3xl font-bold text-gray-900">Application Submitted Successfully!</h2>
              <p className="text-lg text-gray-600">
                Thank you for applying to the <span className="font-semibold text-purple-600">{project.title}</span> internship
              </p>
            </div>
          </div>

          {/* Application Details */}
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 mb-8">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-700 font-medium">Application ID:</span>
                <div
                  className="cursor-pointer hover:scale-105 transition-transform duration-200 hover:shadow-lg"
                  onClick={() => {
                    const trackingUrl = `/track/internship-application/${applicationId}`;
                    window.open(trackingUrl, '_blank');
                  }}
                  title="Click to track your application"
                >
                  <Badge variant="gradient" size="lg" className="font-mono">
                    {applicationId}
                  </Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 font-medium">Project:</span>
                <span className="font-semibold text-purple-600">{project.title}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 font-medium">Applicant:</span>
                <span className="font-semibold">{applicationData.studentDetails.personalInfo.fullName}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 font-medium">Email:</span>
                <span className="font-semibold">{applicationData.email}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700 font-medium">Submitted:</span>
                <span className="font-semibold">{new Date().toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="text-left mb-8">
            <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">What happens next?</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 font-bold text-sm">1</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Application Review</p>
                  <p className="text-sm text-gray-600">Our team will review your application within 2-3 business days</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-purple-600 font-bold text-sm">2</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Initial Screening</p>
                  <p className="text-sm text-gray-600">If shortlisted, you'll receive an email for a technical screening call</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-green-600 font-bold text-sm">3</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Final Interview</p>
                  <p className="text-sm text-gray-600">Meet with the project team and discuss your role in detail</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-yellow-600 font-bold text-sm">4</span>
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Onboarding</p>
                  <p className="text-sm text-gray-600">Welcome to the team! Complete onboarding and start your internship</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-8">
            <h4 className="font-bold text-gray-900 mb-3">Need Help?</h4>
            <div className="space-y-2 text-sm">
              <p className="text-gray-700">
                <span className="font-medium">Email:</span> <EMAIL>
              </p>
              <p className="text-gray-700">
                <span className="font-medium">Phone:</span> +91 4442116715
              </p>
              <p className="text-gray-700">
                <span className="font-medium">Response Time:</span> Within 24 hours
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <Button
              onClick={onComplete}
              variant="gradient"
              size="lg"
              className="w-full"
              glow
            >
              Back to Home
            </Button>
            
            <div className="flex space-x-4">
              <Button
                onClick={() => window.print()}
                variant="outline"
                className="flex-1"
              >
                📄 Print Confirmation
              </Button>
              <Button
                onClick={() => {
                  const subject = `Application Confirmation - ${applicationId}`;
                  const body = `Dear Team,\n\nI have successfully submitted my application for the ${project.title} internship.\n\nApplication ID: ${applicationId}\nApplicant: ${applicationData.studentDetails.personalInfo.fullName}\nEmail: ${applicationData.email}\n\nThank you for considering my application.\n\nBest regards,\n${applicationData.studentDetails.personalInfo.fullName}`;
                  window.location.href = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
                }}
                variant="outline"
                className="flex-1"
              >
                📧 Email Confirmation
              </Button>
            </div>
          </div>

          {/* Social Sharing */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-3">Share your achievement:</p>
            <div className="flex justify-center space-x-4">
              <Button
                onClick={() => {
                  const text = `Just applied for an internship at MSS Internship for ${project.title}! Excited about this opportunity to grow my skills. #MSS Internship #Internship #TechCareer`;
                  window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`, '_blank');
                }}
                variant="ghost"
                size="sm"
                className="text-blue-500 hover:text-blue-600"
              >
                🐦 Twitter
              </Button>
              <Button
                onClick={() => {
                  const text = `Excited to share that I've applied for an internship at MSS Internship for ${project.title}! Looking forward to this amazing learning opportunity.`;
                  window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.origin)}&summary=${encodeURIComponent(text)}`, '_blank');
                }}
                variant="ghost"
                size="sm"
                className="text-blue-700 hover:text-blue-800"
              >
                💼 LinkedIn
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SuccessStep;
