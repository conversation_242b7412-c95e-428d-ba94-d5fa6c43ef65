import React from 'react';
import TeamCard, { Team } from './TeamCard';

interface TeamsGridProps {
  teams: Team[];
  onEditTeam: (team: Team) => void;
  onDeleteTeam: (teamId: string) => void;
}

const TeamsGrid: React.FC<TeamsGridProps> = ({ teams, onEditTeam, onDeleteTeam }) => {
  if (teams.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-8 text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Teams Found</h3>
        <p className="text-gray-500 mb-4">
          There are no teams created yet. Click the "Add Team" button to create your first team.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {teams.map((team) => (
        <TeamCard
          key={team.id}
          team={team}
          onEdit={onEditTeam}
          onDelete={onDeleteTeam}
        />
      ))}
    </div>
  );
};

export default TeamsGrid;
