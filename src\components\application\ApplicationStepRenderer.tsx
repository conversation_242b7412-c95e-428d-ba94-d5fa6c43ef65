import React from 'react';
import { Project } from '../../hooks';
import { ApplicationStep, ApplicationData } from '../../hooks/useApplicationState';
import EmailStep from './EmailStep';
import OTPVerificationStep from './OTPVerificationStep';
import PrerequisitesStep from './PrerequisitesStep';
import StudentDetailsStep from './StudentDetailsStep';
import SuccessStep from './SuccessStep';

interface ApplicationStepRendererProps {
  currentStep: ApplicationStep;
  project: Project;
  applicationData: ApplicationData;
  onStepComplete: (stepData: any) => void;
  onBack: () => void;
}

const ApplicationStepRenderer: React.FC<ApplicationStepRendererProps> = ({
  currentStep,
  project,
  applicationData,
  onStepComplete,
  onBack
}) => {
  switch (currentStep) {
    case 'email':
      return (
        <EmailStep
          project={project}
          onNext={onStepComplete}
          onBack={onBack}
        />
      );
    case 'otp':
      return (
        <OTPVerificationStep
          email={applicationData.email}
          projectId={applicationData.projectId}
          onComplete={onStepComplete}
          onBack={onBack}
        />
      );
    case 'prerequisites':
      return (
        <PrerequisitesStep
          project={project}
          onComplete={onStepComplete}
          onBack={onBack}
        />
      );
    case 'details':
      return (
        <StudentDetailsStep
          project={project}
          initialData={applicationData.studentDetails}
          onComplete={onStepComplete}
          onBack={onBack}
        />
      );
    case 'success':
      return (
        <SuccessStep
          project={project}
          applicationData={applicationData}
          onComplete={onStepComplete}
        />
      );
    default:
      return null;
  }
};

export default ApplicationStepRenderer;
