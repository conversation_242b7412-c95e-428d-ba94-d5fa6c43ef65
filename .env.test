# API Configuration
VITE_API_BASE_URL=http://192.168.29.12:8080

# Environment Mode
# Values: development | test | production
VITE_NODE_ENV=test

# Demo Credentials (Only visible in development/test modes)
# These credentials will ONLY be displayed when VITE_NODE_ENV is 'development' or 'test'
VITE_DEMO_USERNAME=<EMAIL>
VITE_DEMO_PASSWORD=11110000

# reCAPTCHA Configuration (Only active in production mode)
# reCAPTCHA will only be enabled when VITE_NODE_ENV is 'production'
# In development and test modes, captcha verification is bypassed
VITE_RECAPTCHA_SITE_KEY=6Lc97ZIrAAAAAHT-yulPo-qTjN_ELab6rMDTVSBZ
VITE_RECAPTCHA_SECRET_KEY=6Lc97ZIrAAAAAGroCZ6ORrEpJYPqUY8O5o4lOZ-h