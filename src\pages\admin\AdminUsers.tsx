import React, { useState } from 'react';
import {
  <PERSON>Header,
  UsersStats,
  UsersGrid,
  UsersFilter,
  UserFormModal,
  User
} from '../../components/admin-users';
import useUsers from '../../hooks/useUsers';
import { CreateUserRequest, UpdateUserRequest } from '../../services/userService';

const AdminUsers: React.FC = () => {
  const {
    users,
    stats,
    loading,
    filters,
    createUser,
    updateUser,
    deleteUser,
    updateFilters,
    applyFilters,
    resetFilters
  } = useUsers();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);


  const handleAddUser = () => {
    setSelectedUser(null);
    setIsModalOpen(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleDeleteUser = async (userId: number) => {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      try {
        await deleteUser(userId);
      } catch (error) {
        console.error('Failed to delete user:', error);
        alert('Failed to delete user. Please try again.');
      }
    }
  };

  const handleFormSubmit = async (userData: Partial<User>) => {
    try {
      if (selectedUser) {
        // Update existing user
        const updateData: UpdateUserRequest = {
          name: userData.name || '',
          email: userData.email || '',
          phone: userData.phone || '',
          domain: userData.domain || '',
          role: userData.role as 'INTERN' | 'MANAGER' | 'TL',
          portfolioLink: userData.portfolioLink || '',
          githubLink: userData.githubLink || '',
          linkedinLink: userData.linkedinLink || '',
          skills: userData.skills || [],
          status: userData.status as 'ACTIVE' | 'INACTIVE'
        };
        await updateUser(selectedUser.id, updateData);
      } else {
        // Create new user
        const createData: CreateUserRequest = {
          name: userData.name || '',
          email: userData.email || '',
          phone: userData.phone || '',
          domain: userData.domain || '',
          role: userData.role as 'INTERN' | 'MANAGER' | 'TL',
          portfolioLink: userData.portfolioLink || '',
          githubLink: userData.githubLink || '',
          linkedinLink: userData.linkedinLink || '',
          skills: userData.skills || [],
          status: userData.status as 'ACTIVE' | 'INACTIVE'
        };
        await createUser(createData);
      }
      setIsModalOpen(false);
      setSelectedUser(null); // Reset selected user after successful submission
    } catch (error) {
      console.error('Failed to save user:', error);
      alert('Failed to save user. Please try again.');
    }
  };

  // Stats are now provided by the useUsers hook

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <UsersHeader onAddUser={handleAddUser} />

      {/* Stats Cards */}
      <UsersStats stats={stats} />

      {/* Filter Component */}
      <UsersFilter
        filters={filters}
        onFiltersChange={updateFilters}
        onApplyFilters={applyFilters}
        onResetFilters={resetFilters}
        loading={loading}
      />

      {/* Users Grid */}
      <UsersGrid
        users={users}
        onEditUser={handleEditUser}
        onDeleteUser={handleDeleteUser}
        onAddUser={handleAddUser}
      />

      {/* Add/Edit User Modal */}
      <UserFormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleFormSubmit}
        selectedUser={selectedUser}
      />
    </div>
  );
};

export default AdminUsers;
