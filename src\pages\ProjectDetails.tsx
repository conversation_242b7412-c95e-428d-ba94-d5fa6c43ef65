import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { <PERSON><PERSON>, Footer } from '../components/layout';
import { useProjects } from '../hooks';
import {
  ProjectHeader,
  ProjectOverview,
  ProjectTechnologies,
  ProjectRequirements,
  ProjectBenefits,
  ApplicationSidebar,
  LoadingState,
  ErrorState,
  NotFoundState
} from '../components/project-details';

const ProjectDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { project, projectLoading, projectError, getProjectById } = useProjects();

  useEffect(() => {
    if (id) {
      getProjectById(id);
    }
  }, [id, getProjectById]); 

  // Loading state
  if (projectLoading) {
    return <LoadingState />;
  }

  // Error state
  if (projectError) {
    return <ErrorState error={projectError} />;
  }

  // Not found state (if no ID or no project loaded)
  if (!id || !project || !project.id) {
    return <NotFoundState />;
  }


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        {/* Project Header */}
        <ProjectHeader
          project={project}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6 lg:space-y-8">
            {/* Description */}
            <ProjectOverview project={project} />

            {/* Technologies */}
            <ProjectTechnologies project={project} />

            {/* Requirements */}
            <ProjectRequirements project={project} />

            {/* Benefits */}
            <ProjectBenefits project={project} />
          </div>

          {/* Sidebar */}
          <ApplicationSidebar project={project} />
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ProjectDetails;
