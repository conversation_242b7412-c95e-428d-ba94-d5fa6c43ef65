import React from 'react';
import { Card, CardContent } from '../ui';

const ContactHero: React.FC = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-purple-900 via-indigo-900 to-pink-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute top-40 right-20 w-96 h-96 bg-pink-500/20 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-cyan-500/20 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <div className="mb-8">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-white mb-6 leading-tight">
              Get In
              <span className="block gradient-text-accent animate-gradient bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400">
                Touch
              </span>
            </h1>
            <div className="w-32 h-1 bg-gradient-to-r from-cyan-400 to-purple-400 mx-auto rounded-full"></div>
          </div>
          
          <p className="text-xl md:text-3xl text-purple-100 mb-12 max-w-5xl mx-auto leading-relaxed font-light">
            Ready to start your journey with MSS Internship? We're here to help you take the next step in your 
            <span className="gradient-text-accent font-semibold"> tech career</span>.
          </p>

          {/* Quick Contact Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <Card glass hover className="text-center group">
              <CardContent className="pt-8 pb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white text-2xl">📧</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Email Us</h3>
                <p className="text-purple-200 text-sm mb-4">Get in touch via email</p>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-cyan-300 hover:text-white transition-colors font-medium"
                >
                  <EMAIL>
                </a>
              </CardContent>
            </Card>

            <Card glass hover className="text-center group">
              <CardContent className="pt-8 pb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white text-2xl">📞</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Call Us</h3>
                <p className="text-purple-200 text-sm mb-4">Speak with our team</p>
                <a 
                  href="tel:+91 4442116715" 
                  className="text-cyan-300 hover:text-white transition-colors font-medium"
                >
                  +91 4442116715
                </a>
              </CardContent>
            </Card>

            <Card glass hover className="text-center group">
              <CardContent className="pt-8 pb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white text-2xl">💬</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Live Chat</h3>
                <p className="text-purple-200 text-sm mb-4">Chat with support</p>
                <button className="text-cyan-300 hover:text-white transition-colors font-medium">
                  Start Chat
                </button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactHero;
