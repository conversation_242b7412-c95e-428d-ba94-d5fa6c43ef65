import React, { useRef, forwardRef, useImperativeHandle } from 'react';
import Re<PERSON><PERSON><PERSON><PERSON> from 'react-google-recaptcha';
import captchaService from '../../services/captchaService';

interface ReCaptchaProps {
  onVerify: (token: string) => void;
  onError?: (error: string) => void;
  onExpired?: () => void;
  size?: 'compact' | 'normal';
  theme?: 'light' | 'dark';
  className?: string;
}

export interface ReCaptchaRef {
  reset: () => void;
  executeAsync: () => Promise<string | null>;
}

const ReCaptcha = forwardRef<ReCaptchaRef, ReCaptchaProps>(({
  onVerify,
  onError,
  onExpired,
  size = 'normal',
  theme = 'light',
  className = ''
}, ref) => {
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  // Expose reset and executeAsync methods via ref
  useImperativeHandle(ref, () => ({
    reset: () => {
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
      }
    },
    executeAsync: async () => {
      if (recaptchaRef.current) {
        return await recaptchaRef.current.executeAsync();
      }
      return null;
    }
  }));

  // Skip reCAPTCHA in non-production environments
  if (!captchaService.isCaptchaEnabled()) {
    // Automatically call onVerify with a dummy token in dev/test mode
    React.useEffect(() => {
      console.log('🔧 Development/Test Mode: reCAPTCHA disabled');
      const timer = setTimeout(() => {
        onVerify('dev-mode-bypass-token');
      }, 100);
      return () => clearTimeout(timer);
    }, [onVerify]);

    return (
      <div className={`text-sm text-gray-500 italic ${className}`}>
        🔧 Development Mode: reCAPTCHA disabled
      </div>
    );
  }

  const siteKey = captchaService.getSiteKey();
  if (!siteKey) {
    React.useEffect(() => {
      onError?.('reCAPTCHA site key not configured');
    }, [onError]);

    return (
      <div className={`text-sm text-red-600 ${className}`}>
        reCAPTCHA configuration error
      </div>
    );
  }

  const handleChange = (token: string | null) => {
    if (token) {
      console.log('🛡️ Captcha token:', token);
      onVerify(token);
    }
  };

  const handleExpired = () => {
    onExpired?.();
  };

  const handleErrored = () => {
    onError?.('reCAPTCHA error occurred');
  };

  return (
    <div className={className}>
      <ReCAPTCHA
        ref={recaptchaRef}
        sitekey={siteKey}
        size={size}
        theme={theme}
        onChange={handleChange}
        onExpired={handleExpired}
        onErrored={handleErrored}
      />
    </div>
  );
});

ReCaptcha.displayName = 'ReCaptcha';

export default ReCaptcha;
