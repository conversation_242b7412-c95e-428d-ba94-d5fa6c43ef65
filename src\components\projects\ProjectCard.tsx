import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, User, MapPin, Clock } from 'lucide-react';
import Button from '../ui/Button';
import { Card } from '../ui/Card';
import { Project } from '../../types';

interface ProjectCardProps {
  project: Project;
  featured?: boolean;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, featured = false }) => {

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'frontend': return 'bg-blue-100 text-blue-800';
      case 'backend': return 'bg-purple-100 text-purple-800';
      case 'fullstack': return 'bg-indigo-100 text-indigo-800';
      case 'mobile': return 'bg-pink-100 text-pink-800';
      case 'ai/ml': return 'bg-orange-100 text-orange-800';
      case 'devops': return 'bg-teal-100 text-teal-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <Card 
      hover 
      className={`h-full flex flex-col transition-all duration-300 ${
        featured ? 'ring-2 ring-blue-500 ring-opacity-50' : ''
      }`}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex flex-wrap gap-2">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(project.type)}`}>
            {project.type}
          </span>
          {featured && (
            <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
              Featured
            </span>
          )}
        </div>
        <span className="text-sm text-gray-500 flex items-center">
          <Clock className="h-3 w-3 mr-1" />
          {project.duration}
        </span>
      </div>

      {/* Content */}
      <div className="flex-1">
        <h3 className="text-lg font-semibold text-gray-900 mb-2 hover:text-blue-600 transition-colors">
          {project.title}
        </h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {project.description}
        </p>

        {/* Technologies */}
        <div className="flex flex-wrap gap-1 mb-4">
          {project.technologies.slice(0, featured ? 5 : 4).map((tech) => (
            <span 
              key={tech} 
              className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded hover:bg-gray-200 transition-colors"
            >
              {tech}
            </span>
          ))}
          {project.technologies.length > (featured ? 5 : 4) && (
            <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
              +{project.technologies.length - (featured ? 5 : 4)} more
            </span>
          )}
        </div>

        {/* Project Details */}
        <div className="space-y-2 mb-4 text-sm text-gray-600">
          <div className="flex items-center">
            <User className="h-4 w-4 mr-2 text-gray-400" />
            <span>Mentor: {project.mentorName}</span>
          </div>
          <div className="flex items-center">
            <MapPin className="h-4 w-4 mr-2 text-gray-400" />
            <span>{project.openPositions} position{project.openPositions !== 1 ? 's' : ''} available</span>
          </div>
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-2 text-gray-400" />
            <span>Deadline: {formatDate(project.applicationDeadline)}</span>
          </div>
        </div>

        {/* Requirements Preview */}
        {featured && project.requirements.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Key Requirements:</h4>
            <ul className="text-xs text-gray-600 space-y-1">
              {project.requirements.slice(0, 3).map((req, index) => (
                <li key={index} className="flex items-start">
                  <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                  {req}
                </li>
              ))}
              {project.requirements.length > 3 && (
                <li className="text-gray-500 italic">
                  +{project.requirements.length - 3} more requirements
                </li>
              )}
            </ul>
          </div>
        )}
      </div>

      {/* Action Button */}
      <div className="mt-auto">
        <Link to={`/projects/${project.id}/questions`} className="block">
          <Button 
            className={`w-full transition-all duration-200 ${
              featured ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700' : ''
            }`}
          >
            Apply Now
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default ProjectCard;
