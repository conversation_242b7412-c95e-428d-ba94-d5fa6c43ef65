import React from 'react';
import { But<PERSON> } from '../ui';
import { FaPlus } from 'react-icons/fa';

interface ProjectsHeaderProps {
  onAddProject: () => void;
}

const ProjectsHeader: React.FC<ProjectsHeaderProps> = ({ onAddProject }) => {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Project Management</h1>
        <p className="text-gray-600 mt-1">Manage internship projects with dynamic prerequisites and application forms</p>
      </div>
      <Button
        onClick={onAddProject}
        variant="gradient"
        size="lg"
        className="flex items-center space-x-2"
        glow
      >
        <FaPlus />
        <span>Add Project</span>
      </Button>
    </div>
  );
};

export default ProjectsHeader;
