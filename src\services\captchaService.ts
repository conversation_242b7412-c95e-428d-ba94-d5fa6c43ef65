import apiService, { ApiResponse } from './api';

export interface CaptchaVerifyRequest {
  token: string;
}

export interface CaptchaVerifyResponse {
  success: boolean;
  message: string;
  data?: any;
}

class CaptchaService {
  private isProduction(): boolean {
    return import.meta.env.VITE_NODE_ENV === 'production';
  }

  public isCaptchaEnabled(): boolean {
    return this.isProduction();
  }

  public getSiteKey(): string | null {
    if (!this.isProduction()) {
      return null;
    }
    return import.meta.env.VITE_RECAPTCHA_SITE_KEY || null;
  }

  async verifyCaptcha(token: string): Promise<CaptchaVerifyResponse> {
    try {
      // In development/test mode, bypass captcha verification
      if (!this.isProduction()) {
        console.log('🔧 Development/Test Mode: Bypassing captcha verification');
        return {
          success: true,
          message: 'Captcha verification bypassed in development mode'
        };
      }

      // In production mode, verify with backend
      const response = await apiService.post<ApiResponse<any>>(`/captcha/verify?token=${encodeURIComponent(token)}`);

      return {
        success: response.type === 'INFO',
        message: response.message,
        data: response.data
      };
    } catch (error: any) {
      console.error('Captcha verification error:', error);
      
      // In development/test mode, still return success even if API fails
      if (!this.isProduction()) {
        return {
          success: true,
          message: 'Captcha verification bypassed in development mode'
        };
      }

      return {
        success: false,
        message: error.response?.data?.message || 'Captcha verification failed'
      };
    }
  }

}

// Create and export singleton instance
const captchaService = new CaptchaService();
export default captchaService;
