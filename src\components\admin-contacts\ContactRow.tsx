import React from 'react';
import {
  FaPhone,
  FaEnvelope,
  FaEdit,
  FaTrash,
  FaEye,
  FaClock,
  FaExclamationTriangle,
  FaCheckCircle,
  FaUser
} from 'react-icons/fa';
import { Contact } from '../../types/contact';

interface ContactRowProps {
  contact: Contact;
  onView: (contact: Contact) => void;
  onEdit: (contact: Contact) => void;
  onDelete: (contactId: number) => void;
}

const ContactRow: React.FC<ContactRowProps> = ({
  contact,
  onView,
  onEdit,
  onDelete
}) => {
  const getPriorityColor = (priority: Contact['priority']) => {
    switch (priority) {
      case 'CRITICAL': return 'bg-red-100 text-red-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'LOW': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityIcon = (priority: Contact['priority']) => {
    switch (priority) {
      case 'CRITICAL': return <FaExclamationTriangle className="text-red-500" />;
      case 'HIGH': return <FaExclamationTriangle className="text-orange-500" />;
      case 'MEDIUM': return <FaClock className="text-yellow-500" />;
      case 'LOW': return <FaCheckCircle className="text-green-500" />;
      default: return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
              <FaUser className="text-white text-sm" />
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{contact.name}</div>
            <div className="text-sm text-gray-500">{contact.email}</div>
            <div className="text-sm text-gray-500">Contact ID: {contact.id}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="text-sm text-gray-900 max-w-xs truncate">{contact.subject}</div>
        <div className="text-xs text-gray-500">
          Source: {contact.source.replace('_', ' ')}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
         <div className="text-xs text-gray-900">
          Status: {contact.status}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center space-x-2">
          {getPriorityIcon(contact.priority)}
          <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(contact.priority)}`}>
            {contact.priority}
          </span>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {formatDate(contact.createdAt)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onView(contact)}
            className="text-blue-600 hover:text-blue-900"
            title="View Details"
          >
            <FaEye />
          </button>
          <button
            onClick={() => onEdit(contact)}
            className="text-green-600 hover:text-green-900"
            title="Edit"
          >
            <FaEdit />
          </button>
          <a
            href={`mailto:${contact.email}`}
            className="text-purple-600 hover:text-purple-900"
            title="Send Email"
          >
            <FaEnvelope />
          </a>
          {contact.phone && (
            <a
              href={`tel:${contact.phone}`}
              className="text-orange-600 hover:text-orange-900"
              title="Call"
            >
              <FaPhone />
            </a>
          )}
          <button
            onClick={() => onDelete(contact.id)}
            className="text-red-600 hover:text-red-900"
            title="Delete"
          >
            <FaTrash />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default ContactRow;
