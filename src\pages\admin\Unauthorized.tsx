import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../components/ui';
import { FaExclamationTriangle, FaHome, FaSignOutAlt } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';

const Unauthorized: React.FC = () => {
  const navigate = useNavigate();
  const { logout } = useAuth();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleLogout = () => {
    logout();
    navigate('/admin/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <FaExclamationTriangle className="text-red-500 text-3xl" />
        </div>
        
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-8">
          You don't have permission to access this resource. Please contact your administrator if you believe this is an error.
        </p>
        
        <div className="space-y-4">
          <Button
            onClick={handleGoHome}
            variant="primary"
            size="lg"
            className="w-full flex items-center justify-center space-x-2"
          >
            <FaHome />
            <span>Go to Home</span>
          </Button>
          
          <Button
            onClick={handleLogout}
            variant="outline"
            size="lg"
            className="w-full flex items-center justify-center space-x-2 text-red-600 border-red-300 hover:bg-red-50"
          >
            <FaSignOutAlt />
            <span>Logout</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Unauthorized;
