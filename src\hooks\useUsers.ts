import { useState, useEffect } from 'react';
import userService, { User, UsersResponse, CreateUserRequest, UpdateUserRequest } from '../services/userService';
import { useWebSocketField } from '../contexts/WebSocketContext';

interface UserFilters {
  text?: string;
  role?: string;
  status?: string;
}

interface UseUsersReturn {
  users: User[];
  stats: {
    totalUsers: number;
    internCount: number;
    managerCount: number;
    teamLeaderCount: number;
    adminCount: number;
    activeCount: number;
    inactiveCount: number;
    pendingCount: number;
  };
  loading: boolean;
  error: string | null;
  filters: UserFilters;
  refreshUsers: () => Promise<void>;
  createUser: (data: CreateUserRequest) => Promise<void>;
  updateUser: (id: number, data: UpdateUserRequest) => Promise<void>;
  deleteUser: (id: number) => Promise<void>;
  updateFilters: (newFilters: Partial<UserFilters>) => void;
  applyFilters: () => Promise<void>;
  resetFilters: () => Promise<void>;
}

const useUsers = (): UseUsersReturn => {
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState({
    totalUsers: 0,
    internCount: 0,
    managerCount: 0,
    teamLeaderCount: 0,
    adminCount: 0,
    activeCount: 0,
    inactiveCount: 0,
    pendingCount: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<UserFilters>({
    text: '',
    role: 'ALL',
    status: 'ALL'
  });

  const refreshUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response: UsersResponse = await userService.getUsers(filters);
      
      // Transform API users to our User interface
      const transformedUsers = response.data.userDetails.map(apiUser => 
        userService.transformApiUser(apiUser)
      );
      
      setUsers(transformedUsers);
      
      // Calculate stats from API data
      const roleStats = response.data.userCount.reduce((acc, item) => {
        switch (item.role) {
          case 'INTERN':
            acc.internCount = item.count;
            break;
          case 'MANAGER':
            acc.managerCount = item.count;
            break;
          case 'TL':
            acc.teamLeaderCount = item.count;
            break;
          case 'ADMIN':
            acc.adminCount = item.count;
            break;
        }
        return acc;
      }, {
        internCount: 0,
        managerCount: 0,
        teamLeaderCount: 0,
        adminCount: 0,
      });

      const statusStats = response.data.userCountStatus.reduce((acc, item) => {
        switch (item.status) {
          case 'ACTIVE':
            acc.activeCount = item.count;
            break;
          case 'INACTIVE':
            acc.inactiveCount = item.count;
            break;
          case 'PENDING':
            acc.pendingCount = item.count;
            break;
        }
        return acc;
      }, {
        activeCount: 0,
        inactiveCount: 0,
        pendingCount: 0,
      });

      const totalUsers = transformedUsers.length;

      setStats({
        totalUsers,
        ...roleStats,
        ...statusStats,
      });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  const createUser = async (data: CreateUserRequest) => {
    try {
      await userService.createUser(data);
      await refreshUsers(); // Refresh the list after creation
    } catch (err: any) {
      setError(err.message || 'Failed to create user');
      throw err;
    }
  };

  const updateUser = async (id: number, data: UpdateUserRequest) => {
    try {
      await userService.updateUser(id, data);
      await refreshUsers(); // Refresh the list after update
    } catch (err: any) {
      setError(err.message || 'Failed to update user');
      throw err;
    }
  };

  const deleteUser = async (id: number) => {
    try {
      await userService.deleteUser(id);
      await refreshUsers(); // Refresh the list after deletion
    } catch (err: any) {
      setError(err.message || 'Failed to delete user');
      throw err;
    }
  };

  // Filter functions
  const updateFilters = (newFilters: Partial<UserFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const applyFilters = async () => {
    await refreshUsers();
  };

  const resetFilters = async () => {
    // Reset filters to default state
    setFilters({ text: '', role: 'ALL', status: 'ALL' });
    // Immediately fetch users without any filters
    try {
      setLoading(true);
      const response: UsersResponse = await userService.getUsers();

      // Transform API users to our User interface
      const transformedUsers = response.data.userDetails.map(apiUser =>
        userService.transformApiUser(apiUser)
      );

      setUsers(transformedUsers);

      // Calculate stats from API data
      const roleStats = response.data.userCount.reduce((acc, item) => {
        switch (item.role) {
          case 'INTERN':
            acc.internCount = item.count;
            break;
          case 'MANAGER':
            acc.managerCount = item.count;
            break;
          case 'TL':
            acc.teamLeaderCount = item.count;
            break;
          case 'ADMIN':
            acc.adminCount = item.count;
            break;
        }
        return acc;
      }, {
        internCount: 0,
        managerCount: 0,
        teamLeaderCount: 0,
        adminCount: 0,
      });

      const statusStats = response.data.userCountStatus.reduce((acc, item) => {
        switch (item.status) {
          case 'ACTIVE':
            acc.activeCount = item.count;
            break;
          case 'INACTIVE':
            acc.inactiveCount = item.count;
            break;
          case 'PENDING':
            acc.pendingCount = item.count;
            break;
        }
        return acc;
      }, {
        activeCount: 0,
        inactiveCount: 0,
        pendingCount: 0,
      });

      const totalUsers = transformedUsers.length;

      setStats({
        totalUsers,
        ...roleStats,
        ...statusStats,
      });

      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  // WebSocket integration for real-time updates
  const userMessage = useWebSocketField('USER');

  useEffect(() => {
    if (userMessage) {
      console.log('📩 User WebSocket update:', userMessage);

      switch (userMessage.event) {
        case 'ADD':
          // Refresh users to get the new user
          refreshUsers();
          break;
        case 'UPDATE':
          // Update specific user or refresh all
          if (userMessage.data?.users) {
            setUsers(userMessage.data.users);
            if (userMessage.data.userStats) {
              setStats(userMessage.data.userStats);
            }
          } else {
            refreshUsers();
          }
          break;
        case 'DELETE':
          // Refresh users to remove deleted user
          refreshUsers();
          break;
        default:
          console.log('Unknown user event:', userMessage.event);
      }
    }
  }, [userMessage]);

  // Load users on mount
  useEffect(() => {
    refreshUsers();
  }, []);

  return {
    users,
    stats,
    loading,
    error,
    filters,
    refreshUsers,
    createUser,
    updateUser,
    deleteUser,
    updateFilters,
    applyFilters,
    resetFilters,
  };
};

export default useUsers;
