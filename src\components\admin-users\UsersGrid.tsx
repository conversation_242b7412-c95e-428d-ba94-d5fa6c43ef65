import React from 'react';
import UserCard, { User } from './UserCard';
import EmptyState from './EmptyState';

interface UsersGridProps {
  users: User[];
  onEditUser: (user: User) => void;
  onDeleteUser: (userId: number) => void;
  onAddUser: () => void;
}

const UsersGrid: React.FC<UsersGridProps> = ({ users, onEditUser, onDeleteUser, onAddUser }) => {
  if (users.length === 0) {
    return <EmptyState onAddUser={onAddUser} />;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {users.map((user) => (
        <UserCard
          key={user.id}
          user={user}
          onEdit={onEditUser}
          onDelete={onDeleteUser}
        />
      ))}
    </div>
  );
};

export default UsersGrid;
