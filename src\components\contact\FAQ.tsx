import React, { useState } from 'react';
import { <PERSON>, CardContent } from '../ui';
import {
  FaQuestionCircle,
  FaEdit,
  FaGraduationCap,
  FaTrophy,
  FaTools,
  FaCheckCircle,
  FaRocket,
  FaComments,
  FaEnvelope,
  FaPhone
} from 'react-icons/fa';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

const FAQ: React.FC = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [activeCategory, setActiveCategory] = useState('all');

  const faqData: FAQItem[] = [
    {
      question: 'How do I apply for an internship at MSS Internship?',
      answer: 'You can apply by browsing our available projects on the homepage, clicking "View Details" on any project that interests you, and then clicking "Apply Now". You\'ll go through a multi-step process including prerequisites questions, submitting your details with resume, and email verification.',
      category: 'application'
    },
    {
      question: 'What are the prerequisites for MSS Internship internships?',
      answer: 'Prerequisites vary by project, but generally include basic programming knowledge, willingness to learn, and time commitment. Specific requirements are listed for each project and will be assessed during the application process.',
      category: 'application'
    },
    {
      question: 'How long do MSS Internship internships last?',
      answer: 'Internship durations vary by project, typically ranging from 2-6 months. Each project listing shows the expected duration. Some projects may offer extensions based on performance and availability.',
      category: 'program'
    },
    {
      question: 'Are MSS Internship internships paid?',
      answer: 'Yes, most of our internships offer competitive stipends. The compensation details are discussed during the final interview process and vary based on the project complexity and your experience level.',
      category: 'program'
    },
    {
      question: 'Can I work remotely during my internship?',
      answer: 'Yes, we offer flexible work arrangements including remote, hybrid, and on-site options. The work arrangement depends on the specific project requirements and will be discussed during the application process.',
      category: 'program'
    },
    {
      question: 'What certificates will I receive upon completion?',
      answer: 'Upon successful completion, you\'ll receive certificates from our global partners: Inspeedia Japan, Mothercode India, and Ziliconcloud USA. These are industry-recognized certifications that enhance your professional profile.',
      category: 'certificates'
    },
    {
      question: 'How are the certificates verified?',
      answer: 'All our certificates are blockchain-verified and can be authenticated through our partner organizations\' verification systems. You\'ll receive digital certificates with unique verification codes.',
      category: 'certificates'
    },
    {
      question: 'What kind of mentorship can I expect?',
      answer: 'Each intern is assigned a dedicated mentor from our team of senior developers and industry experts. You\'ll have regular 1-on-1 sessions, code reviews, and career guidance throughout your internship.',
      category: 'support'
    },
    {
      question: 'What happens if I need technical support during my internship?',
      answer: 'We provide 24/7 technical support through multiple channels including email, chat, and phone. Each project also has dedicated team leads who can assist with project-specific questions.',
      category: 'support'
    },
    {
      question: 'Can international students apply?',
      answer: 'Yes, we welcome applications from international students. However, please ensure you have the necessary work authorization if the internship requires on-site presence in specific locations.',
      category: 'eligibility'
    },
    {
      question: 'What is the selection process like?',
      answer: 'Our selection process includes: 1) Online application with prerequisites assessment, 2) Technical screening call, 3) Project-specific interview with the team, and 4) Final onboarding. The entire process typically takes 1-2 weeks.',
      category: 'application'
    },
    {
      question: 'Do you provide job placement assistance after internship completion?',
      answer: 'Yes, we have partnerships with leading tech companies and provide job placement assistance, including resume reviews, interview preparation, and direct referrals to our partner organizations.',
      category: 'career'
    }
  ];

  const categories = [
    { id: 'all', label: 'All Questions', icon: <FaQuestionCircle /> },
    { id: 'application', label: 'Application Process', icon: <FaEdit /> },
    { id: 'program', label: 'Program Details', icon: <FaGraduationCap /> },
    { id: 'certificates', label: 'Certificates', icon: <FaTrophy /> },
    { id: 'support', label: 'Support', icon: <FaTools /> },
    { id: 'eligibility', label: 'Eligibility', icon: <FaCheckCircle /> },
    { id: 'career', label: 'Career Support', icon: <FaRocket /> }
  ];

  const filteredFAQs = activeCategory === 'all' 
    ? faqData 
    : faqData.filter(item => item.category === activeCategory);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-black text-gray-900 mb-6">
            Frequently Asked <span className="gradient-text-primary">Questions</span>
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Find answers to common questions about our internship programs
          </p>
        </div>

        {/* Category Filter */}
        <div className="mb-12">
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  activeCategory === category.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="mr-2 flex items-center">{category.icon}</span>
                <span>{category.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {filteredFAQs.map((item, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-0">
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full text-left p-6 hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 pr-4">
                      {item.question}
                    </h3>
                    <div className={`transform transition-transform duration-200 ${
                      openItems.includes(index) ? 'rotate-180' : ''
                    }`}>
                      <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </button>
                
                {openItems.includes(index) && (
                  <div className="px-6 pb-6">
                    <div className="border-t border-gray-200 pt-4">
                      <p className="text-gray-700 leading-relaxed">
                        {item.answer}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Still Have Questions */}
        <div className="mt-16">
          <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaComments className="text-white text-2xl" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Still Have Questions?</h3>
              <p className="text-gray-700 mb-6 max-w-2xl mx-auto">
                Can't find the answer you're looking for? Our support team is here to help you with any questions about our internship programs.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 font-medium"
                >
                  <FaEnvelope />
                  <span>Email Support</span>
                </a>
                <a
                  href="tel:+91 4442116715"
                  className="inline-flex items-center space-x-2 px-6 py-3 border-2 border-purple-500 text-purple-600 rounded-xl hover:bg-purple-50 transition-all duration-300 font-medium"
                >
                  <FaPhone />
                  <span>Call Us</span>
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
