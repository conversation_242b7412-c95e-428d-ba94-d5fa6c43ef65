import apiService, { ApiResponse } from './api';
import { Project, ProjectsResponse, ProjectDetailResponse, PrerequisiteQuestion, ProjectFormData } from '../types/projects';

class ProjectService {
  // Fetch all projects (common endpoint for both admin and public)
  async fetchProjects(filters?: { status?: string; text?: string }): Promise<{ projects: Project[]; stats?: any }> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters?.status && filters.status !== 'ALL') {
        queryParams.append('status', filters.status);
      }

      if (filters?.text && filters.text.trim()) {
        queryParams.append('text', filters.text.trim());
      }

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/project?${queryString}` : '/project';

      const response = await apiService.get<ApiResponse<ProjectsResponse>>(endpoint);
      
      if (!response.data) {
        throw new Error('No data received');
      }

      const apiData = response.data;
      console.log('Project data:', apiData);
      // Transform API response to match our Project interface
      const transformedProjects = apiData.projectDetailList.map(project => ({
        id: project.id.toString(),
        title: project.title,
        description: project.description,
        image: project.image || '',
        technologies: project.technologies || [],
        duration: project.duration,
        category: project.type || project.category || '',
        requirements: project.requirements || [],
        gains: project.gains || [],
        maxInterns: project.maxInterns || 0,
        applicationCount: project.applicationCount || 0,
        status: project.status,
        prerequisiteQuestions: [],
        questionCount: project.questionCount || 0
      }));

      // Return projects with optional stats (for admin use)
      const stats = apiData.projectmanagementCount ? {
        total: apiData.projectmanagementCount.totalProjectCount,
        active: apiData.projectmanagementCount.activeProjectCount,
        inActive: apiData.projectmanagementCount.inactiveProjectCount,
        closed: apiData.projectmanagementCount.closedProjectCount,
      } : undefined;

      return { projects: transformedProjects, stats };
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw new Error('Failed to load projects');
    }
  }

  // Fetch project by ID (common for both admin and public)
  async getProjectById(id: string): Promise<Project> {
    try {
      const response = await apiService.get<ApiResponse<ProjectDetailResponse>>(`/project/detail/${id}`);
      
      if (!response.data) {
        throw new Error('No data received');
      }

      const data = response.data;
      console.log('Project data:', data);
      return {
        id,
        title: data.detailedProjectInfo.title,
        description: data.detailedProjectInfo.description,
        image: data.detailedProjectInfo.image || '',
        technologies: data.detailedProjectInfo.technologies,
        duration: data.detailedProjectInfo.duration,
        category: data.detailedProjectInfo.type,
        requirements: data.detailedProjectInfo.requirements,
        gains: data.detailedProjectInfo.gains,
        maxInterns: data.detailedProjectInfo.maxInterns,
        status: data.detailedProjectInfo.status,
        applicationCount: data.detailedProjectInfo.applicationCount,
        questionCount: data.detailedProjectInfo.questionCount,
        prerequisiteQuestions: data.prerequisiteQuestions.map((q: PrerequisiteQuestion) => ({
          text: q.text,
          questionType: q.questionType,
          options: q.options,
          required: q.required
        }))
      };
    } catch (error) {
      console.error('Error fetching project details:', error);
      throw new Error('Failed to fetch project details');
    }
  }

  // Admin-only methods
  async createProject(projectData: ProjectFormData): Promise<{ success: boolean; error?: string }> {
    try {
      // Create FormData for file upload
      const formData = new FormData();

      // Add image file if provided
      if (projectData.image) {
        formData.append('file', projectData.image);
      }

      // Add all other data as a single JSON string under 'data' key
      const projectPayload = {
        title: projectData.title,
        description: projectData.description,
        technologies: projectData.technologies,
        category: projectData.category,
        status: projectData.status,
        gains: projectData.gains,
        requirements: projectData.requirements,
        maxInterns: projectData.maxInterns,
        duration: projectData.duration,
        prerequisiteQuestions: projectData.prerequisiteQuestions.map(q => ({
          text: q.text,
          questionType: q.questionType,
          options: q.options,
          required: q.required
        }))
      };

      // Add JSON data as a single 'data' part
      formData.append('data', JSON.stringify(projectPayload));

      const response = await apiService.postFormData<ApiResponse>('/admin/project/create-project', formData);

      if (response.status === 201) {
        return { success: true };
      }
      return { 
        success: false, 
        error: response.message || 'Failed to add project' 
      };
    } catch (err: any) {
      console.error('Error adding project:', err);
      return { 
        success: false, 
        error: err.response?.data?.message || 'Failed to add project' 
      };
    }
  }

  async updateProject(id: string, projectData: ProjectFormData): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Updating project:', projectData);

      // Create FormData for file upload
      const formData = new FormData();

      // Add image file if provided
      if (projectData.image) {
        formData.append('file', projectData.image);
      }

      formData.append('deleteImage', projectData.deleteImage.toString());

      // Add all other data as a single JSON string under 'data' key
      const projectPayload = {
        title: projectData.title,
        description: projectData.description,
        technologies: projectData.technologies,
        category: projectData.category,
        status: projectData.status,
        gains: projectData.gains,
        requirements: projectData.requirements,
        maxInterns: projectData.maxInterns,
        duration: projectData.duration,
        prerequisiteQuestions: projectData.prerequisiteQuestions?.map(q => ({
          text: q.text,
          questionType: q.questionType,
          options: q.options,
          required: q.required
        })) || []
      };

      // Add JSON data as a single 'data' part
      formData.append('data', JSON.stringify(projectPayload));

      const response = await apiService.postFormData<ApiResponse>(`/admin/project/update-project/${id}`, formData);

      if (response.status === 200) {
        return { success: true };
      }
      return { 
        success: false, 
        error: response.message || 'Failed to update project' 
      };
    } catch (err: any) {
      console.error('Error updating project:', err);
      return { 
        success: false, 
        error: err.response?.data?.message || 'Failed to update project' 
      };
    }
  }

  async deleteProject(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await apiService.delete<ApiResponse>(`/admin/project/${id}`);

      if (response.status === 200) {
        return { success: true };
      }
      return { 
        success: false, 
        error: response.message || 'Failed to delete project' 
      };
    } catch (err: any) {
      console.error('Error deleting project:', err);
      return { 
        success: false, 
        error: err.response?.data?.message || 'Failed to delete project' 
      };
    }
  }

  // Utility methods
  filterProjectsByCategory(projects: Project[], category: string): Project[] {
    return projects.filter(project => project.category === category);
  }

  filterProjectsByStatus(projects: Project[], status: string): Project[] {
    return projects.filter(project => project.status === status.toUpperCase());
  }

  getOpenProjects(projects: Project[]): Project[] {
    return projects.filter(project => project.status === 'ACTIVE');
  }
}

// Create and export singleton instance
const projectService = new ProjectService();
export default projectService;
