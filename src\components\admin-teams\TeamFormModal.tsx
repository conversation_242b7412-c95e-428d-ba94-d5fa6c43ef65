import React, { useState, useEffect } from 'react';
import { Modal, Button, Input } from '../ui';
import { Team } from './TeamCard';
import { User } from '../admin-users/UserCard';

interface Project {
  projectId: number;
  projectName: string;
}

interface TeamFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (teamData: Partial<Team>) => void;
  selectedTeam: Team | null;
  users: User[];
  projects: Project[];
}

const TeamFormModal: React.FC<TeamFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  selectedTeam,
  users,
  projects
}) => {
  const [formData, setFormData] = useState<Partial<Team>>({});
  const [selectedManagers, setSelectedManagers] = useState<User[]>([]);
  const [selectedTeamLeaders, setSelectedTeamLeaders] = useState<User[]>([]);
  const [selectedInterns, setSelectedInterns] = useState<User[]>([]);
  const [selectedProjects, setSelectedProjects] = useState<number[]>([]);

  // Filter users by role and status
  const managers = users.filter(user => user.role === 'MANAGER');
  const teamLeaders = users.filter(user => user.role === 'TL');
  const interns = users.filter(user => user.role === 'INTERN');

  useEffect(() => {
    if (selectedTeam) {
      setFormData(selectedTeam);
      setSelectedManagers(selectedTeam.managers || []);
      setSelectedTeamLeaders(selectedTeam.teamLeaders || []);
      setSelectedInterns(selectedTeam.interns || []);

      // Convert project names to project IDs for selection
      const projectIds = (selectedTeam.projects || []).map(projectName => {
        const project = projects.find(p => p.projectName === projectName);
        return project ? project.projectId : null;
      }).filter(id => id !== null) as number[];

      setSelectedProjects(projectIds);
    } else {
      setFormData({
        name: '',
        description: '',
        status: 'active',
      });
      setSelectedManagers([]);
      setSelectedTeamLeaders([]);
      setSelectedInterns([]);
      setSelectedProjects([]);
    }
  }, [selectedTeam, projects]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleManagerToggle = (manager: User) => {
    setSelectedManagers(prev => {
      const exists = prev.find(m => m.id === manager.id);
      if (exists) {
        return prev.filter(m => m.id !== manager.id);
      } else {
        return [...prev, manager];
      }
    });
  };

  const handleTeamLeaderToggle = (teamLeader: User) => {
    setSelectedTeamLeaders(prev => {
      const exists = prev.find(tl => tl.id === teamLeader.id);
      if (exists) {
        return prev.filter(tl => tl.id !== teamLeader.id);
      } else {
        return [...prev, teamLeader];
      }
    });
  };

  const handleInternToggle = (intern: User) => {
    setSelectedInterns(prev => {
      const exists = prev.find(i => i.id === intern.id);
      if (exists) {
        return prev.filter(i => i.id !== intern.id);
      } else {
        return [...prev, intern];
      }
    });
  };

  const handleProjectToggle = (projectId: number) => {
    setSelectedProjects(prev => {
      if (prev.includes(projectId)) {
        return prev.filter(p => p !== projectId);
      } else {
        return [...prev, projectId];
      }
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name) {
      alert('Please fill in the team name');
      return;
    }

    // Convert project IDs back to project names for the Team interface
    const selectedProjectNames = selectedProjects.map(projectId => {
      const project = projects.find(p => p.projectId === projectId);
      return project ? project.projectName : '';
    }).filter(name => name !== '');

    const teamData: Partial<Team> = {
      ...formData,
      managers: selectedManagers,
      teamLeaders: selectedTeamLeaders,
      interns: selectedInterns,
      projects: selectedProjectNames,
      createdDate: selectedTeam?.createdDate || new Date().toISOString()
    };

    onSubmit(teamData);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={selectedTeam ? 'Edit Team' : 'Add New Team'}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Team Name *"
            value={formData.name || ''}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter team name"
            required
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status *
            </label>
            <select
              value={formData.status || 'active'}
              onChange={(e) => handleInputChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            value={formData.description || ''}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter team description"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Managers Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Managers ({selectedManagers.length} selected)
          </label>
          <div className="max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-3 space-y-2">
            {managers.length > 0 ? (
              managers.map(manager => (
                <label key={manager.id} className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedManagers.some(m => m.id === manager.id)}
                    onChange={() => handleManagerToggle(manager)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{manager.name}</span>
                </label>
              ))
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">
                No active managers available for selection
              </p>
            )}
          </div>
        </div>

        {/* Team Leaders Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Team Leaders ({selectedTeamLeaders.length} selected)
          </label>
          <div className="max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-3 space-y-2">
            {teamLeaders.length > 0 ? (
              teamLeaders.map(teamLeader => (
                <label key={teamLeader.id} className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedTeamLeaders.some(tl => tl.id === teamLeader.id)}
                    onChange={() => handleTeamLeaderToggle(teamLeader)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{teamLeader.name}</span>
                </label>
              ))
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">
                No active team leaders available for selection
              </p>
            )}
          </div>
        </div>

        {/* Team Members Selection - Interns Only */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Team Members - Interns ({selectedInterns.length} selected)
          </label>
          <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-3 space-y-2">
            {interns.length > 0 ? (
              interns.map(intern => (
                <label key={intern.id} className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedInterns.some(i => i.id === intern.id)}
                    onChange={() => handleInternToggle(intern)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{intern.name}</span>
                </label>
              ))
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">
                No active interns available for selection
              </p>
            )}
          </div>
        </div>

        {/* Projects Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Assigned Projects ({selectedProjects.length} selected)
          </label>
          <div className="max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-3 space-y-2">
            {projects.map(project => (
              <label key={project.projectId} className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedProjects.includes(project.projectId)}
                  onChange={() => handleProjectToggle(project.projectId)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">{project.projectName}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" variant="gradient">
            {selectedTeam ? 'Update Team' : 'Create Team'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default TeamFormModal;
