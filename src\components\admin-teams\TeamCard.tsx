import React from 'react';
import { <PERSON>, CardContent, Badge } from '../ui';
import { FaEdit, FaTrash, FaUsers, FaUserTie, FaProjectDiagram } from 'react-icons/fa';
import { User } from '../admin-users/UserCard';

export interface Team {
  id: string;
  name: string;
  description: string;
  managers: User[];
  teamLeaders: User[];
  interns: User[];
  projects: string[];
  status: 'active' | 'inactive';
  createdDate: string;
}

interface TeamCardProps {
  team: Team;
  onEdit: (team: Team) => void;
  onDelete: (teamId: string) => void;
}

const TeamCard: React.FC<TeamCardProps> = ({ team, onEdit, onDelete }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'manager':
        return <FaUserTie className="text-purple-600" />;
      case 'team_leader':
        return <FaUsers className="text-blue-600" />;
      default:
        return <FaUsers className="text-gray-600" />;
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">{team.name}</h3>
            <p className="text-sm text-gray-600 mb-2">{team.description}</p>
            <Badge className={getStatusColor(team.status)}>
              {team.status.toUpperCase()}
            </Badge>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => onEdit(team)}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              title="Edit Team"
            >
              <FaEdit />
            </button>
            <button
              onClick={() => onDelete(team.id)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              title="Delete Team"
            >
              <FaTrash />
            </button>
          </div>
        </div>

        {/* Team Leadership */}
        <div className="space-y-3 mb-4">
          {/* Managers */}
          <div>
            <div className="flex items-center space-x-2 mb-1">
              {getRoleIcon('manager')}
              <p className="text-xs text-gray-500">Managers ({team.managers.length})</p>
            </div>
            <div className="flex flex-wrap gap-1">
              {team.managers.slice(0, 2).map((manager) => (
                <Badge key={manager.id} className="bg-purple-50 text-purple-700 text-xs">
                  {manager.name}
                </Badge>
              ))}
              {team.managers.length > 2 && (
                <Badge className="bg-gray-50 text-gray-600 text-xs">
                  +{team.managers.length - 2} more
                </Badge>
              )}
            </div>
          </div>

          {/* Team Leaders */}
          <div>
            <div className="flex items-center space-x-2 mb-1">
              {getRoleIcon('team_leader')}
              <p className="text-xs text-gray-500">Team Leaders ({team.teamLeaders.length})</p>
            </div>
            <div className="flex flex-wrap gap-1">
              {team.teamLeaders.slice(0, 2).map((teamLeader) => (
                <Badge key={teamLeader.id} className="bg-blue-50 text-blue-700 text-xs">
                  {teamLeader.name}
                </Badge>
              ))}
              {team.teamLeaders.length > 2 && (
                <Badge className="bg-gray-50 text-gray-600 text-xs">
                  +{team.teamLeaders.length - 2} more
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Team Stats */}
        <div className="grid grid-cols-4 gap-2 mb-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-purple-600 mb-1">
              <FaUserTie className="text-sm" />
              <span className="text-lg font-semibold">{team.managers.length}</span>
            </div>
            <p className="text-xs text-gray-500">Managers</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-blue-600 mb-1">
              <FaUsers className="text-sm" />
              <span className="text-lg font-semibold">{team.teamLeaders.length}</span>
            </div>
            <p className="text-xs text-gray-500">Leaders</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-cyan-600 mb-1">
              <FaUsers className="text-sm" />
              <span className="text-lg font-semibold">{team.interns.length}</span>
            </div>
            <p className="text-xs text-gray-500">Interns</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 text-green-600 mb-1">
              <FaProjectDiagram className="text-sm" />
              <span className="text-lg font-semibold">{team.projects.length}</span>
            </div>
            <p className="text-xs text-gray-500">Projects</p>
          </div>
        </div>

        {/* Team Members Preview */}
        <div>
          <p className="text-sm font-medium text-gray-700 mb-2">Team Interns:</p>
          <div className="flex flex-wrap gap-1">
            {team.interns.slice(0, 3).map((intern) => (
              <Badge key={intern.id} className="bg-blue-50 text-blue-700 text-xs">
                {intern.name}
              </Badge>
            ))}
            {team.interns.length > 3 && (
              <Badge className="bg-gray-50 text-gray-600 text-xs">
                +{team.interns.length - 3} more
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TeamCard;
