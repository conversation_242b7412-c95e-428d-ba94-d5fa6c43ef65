import React from 'react';
import { FaUsers } from 'react-icons/fa';

interface ProjectStats {
  total: number;
  active: number;
  inActive: number;
  closed: number;
}

interface ProjectsStatsProps {
  stats: ProjectStats;
}

const ProjectsStats: React.FC<ProjectsStatsProps> = ({ stats }) => {
  const statItems = [
    {
      id: 'total',
      value: stats.total,
      label: 'Total Projects',
      icon: <FaUsers className="text-blue-600" />,
      bgColor: 'bg-blue-100'
    },
    {
      id: 'active',
      value: stats.active,
      label: 'Active',
      icon: <div className="w-3 h-3 bg-green-500 rounded-full"></div>,
      bgColor: 'bg-green-100'
    },
    {
      id: 'inactive',
      value: stats.inActive,
      label: 'InActive',
      icon: <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>,
      bgColor: 'bg-yellow-100'
    },
    {
      id: 'closed',
      value: stats.closed,
      label: 'Closed',
      icon: <div className="w-3 h-3 bg-red-500 rounded-full"></div>,
      bgColor: 'bg-red-100'
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {statItems.map((item) => (
        <div key={item.id} className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 ${item.bgColor} rounded-lg flex items-center justify-center`}>
              {item.icon}
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">{item.value}</p>
              <p className="text-sm text-gray-600">{item.label}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProjectsStats;
