import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  glass?: boolean;
  gradient?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  hover = false,
  glass = false,
  gradient = false
}) => {
  const baseClasses = 'rounded-2xl transition-all duration-500';

  let backgroundClasses = 'bg-white/80 backdrop-blur-sm border border-white/20 shadow-xl';

  if (glass) {
    backgroundClasses = 'glass shadow-2xl';
  } else if (gradient) {
    backgroundClasses = 'bg-gradient-to-br from-white/90 via-purple-50/80 to-pink-50/80 backdrop-blur-sm border border-white/30 shadow-2xl';
  }

  const hoverClasses = hover ? 'hover:shadow-2xl hover:scale-105 hover:-translate-y-2 hover:rotate-1' : '';
  const classes = `${baseClasses} ${backgroundClasses} ${hoverClasses} ${className}`;

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '' }) => {
  return (
    <div className={`px-8 py-6 border-b border-white/20 ${className}`}>
      {children}
    </div>
  );
};

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

const CardContent: React.FC<CardContentProps> = ({ children, className = '' }) => {
  return (
    <div className={`px-8 py-6 ${className}`}>
      {children}
    </div>
  );
};

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

const CardFooter: React.FC<CardFooterProps> = ({ children, className = '' }) => {
  return (
    <div className={`px-8 py-6 border-t border-white/20 ${className}`}>
      {children}
    </div>
  );
};

export { Card, CardHeader, CardContent, CardFooter };
