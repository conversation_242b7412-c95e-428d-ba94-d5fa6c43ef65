import { useState, useEffect } from 'react';
import { Project, ProjectFormData } from '../types/projects';
import projectService from '../services/projectService';
import { useWebSocketField } from '../contexts/WebSocketContext';

interface ProjectFilters {
  status?: string;
  text?: string;
}

const useAdminProjects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ProjectFilters>({
    status: 'ALL',
    text: ''
  });
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inActive: 0,
    closed: 0,
  });

  // WebSocket integration for real-time updates
  const projectMessage = useWebSocketField('PROJECT');

  useEffect(() => {
    if (projectMessage) {
      console.log('📩 Project WebSocket update:', projectMessage);

      switch (projectMessage.event) {
        case 'ADD':
          // Refresh projects to get the new project
          fetchProjects();
          break;
        case 'UPDATE':
          // Update specific project or refresh all
          if (projectMessage.data?.projects) {
            setProjects(projectMessage.data.projects);
            if (projectMessage.data.projectStats) {
              setStats(projectMessage.data.projectStats);
            }
          } else {
            fetchProjects();
          }
          break;
        case 'DELETE':
          // Refresh projects to remove deleted project
          fetchProjects();
          break;
        default:
          console.log('Unknown project event:', projectMessage.event);
      }
    }
  }, [projectMessage]);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const { projects: fetchedProjects, stats: fetchedStats } = await projectService.fetchProjects(filters);

      setProjects(fetchedProjects);

      // Update stats if available
      if (fetchedStats) {
        setStats(fetchedStats);
      }

      setError(null);
    } catch (err) {
      setError('Failed to load projects');
      console.error('Error fetching projects:', err);
    } finally {
      setLoading(false);
    }
  };

  const getProjectById = async (id: string): Promise<Project> => {
    return await projectService.getProjectById(id);
  };

  const addProject = async (projectData: ProjectFormData) => {
    const result = await projectService.createProject(projectData);
    if (result.success) {
      await fetchProjects(); // Refresh the list
    }
    return result;
  };

  const updateProject = async (id: string, projectData: ProjectFormData) => {
    const result = await projectService.updateProject(id, projectData);
    if (result.success) {
      await fetchProjects(); // Refresh the list
    }
    return result;
  };

  const deleteProject = async (id: string) => {
    const result = await projectService.deleteProject(id);
    if (result.success) {
      await fetchProjects(); // Refresh the list
    }
    return result;
  };

  const getProjectsByCategory = (category: string): Project[] => {
    return projectService.filterProjectsByCategory(projects, category);
  };

  const getProjectsByStatus = (status: string): Project[] => {
    return projectService.filterProjectsByStatus(projects, status);
  };

  const getOpenProjects = (): Project[] => {
    return projectService.getOpenProjects(projects);
  };

  // Filter functions
  const updateFilters = (newFilters: Partial<ProjectFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const applyFilters = async () => {
    await fetchProjects();
  };

  const resetFilters = async () => {
    // Reset filters to default state
    setFilters({ status: 'ALL', text: '' });
    // Immediately fetch projects without any filters
    try {
      setLoading(true);
      const { projects: fetchedProjects, stats: fetchedStats } = await projectService.fetchProjects();

      setProjects(fetchedProjects);

      // Update stats if available
      if (fetchedStats) {
        setStats(fetchedStats);
      }

      setError(null);
    } catch (err) {
      setError('Failed to load projects');
      console.error('Error fetching projects:', err);
    } finally {
      setLoading(false);
    }
  };

  return {
    projects,
    loading,
    error,
    stats,
    filters,
    addProject,
    updateProject,
    deleteProject,
    getProjectById,
    getProjectsByCategory,
    getProjectsByStatus,
    getOpenProjects,
    getStats: () => stats,
    refreshProjects: fetchProjects,
    updateFilters,
    applyFilters,
    resetFilters
  };
};

export default useAdminProjects;