import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from '../ui';
import { <PERSON>a<PERSON>sers, FaClock, FaCheckCircle, FaQuestionCircle } from 'react-icons/fa';
import { Project } from '../../types/projects';

interface ProjectViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
}

const ProjectViewModal: React.FC<ProjectViewModalProps> = ({
  isOpen,
  onClose,
  project
}) => {
  if (!project) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Open':
        return 'bg-green-100 text-green-800';
      case 'Closed':
        return 'bg-red-100 text-red-800';
      case 'Full':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // const calculateFillPercentage = (current: number, max: number) => {
  //   return max > 0 ? Math.round((current / max) * 100) : 0;
  // };

  const getQuestionTypeInternshipel = (type: string) => {
    switch (type) {
      case 'TEXT':
        return 'Text Input';
      case 'SELECT':
        return 'Single Choice';
      case 'MULTISELECT':
        return 'Multiple Choice';
      case 'BOOLEAN':
        return 'Yes/No';
      default:
        return type;
    }
  };

  return (
    <Modal title="Project Details" isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6 max-h-[90vh] overflow-y-auto">

        {/* Project Header */}
        <div className="mb-6">
          <div className="flex items-start space-x-4">
            {project.image ? (
              <img
                src={project.image}
                alt={project.title}
                className="w-24 h-24 object-cover rounded-lg flex-shrink-0"
              />
            ) : (
              <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center rounded-lg flex-shrink-0">
                <div className="text-center text-gray-500">
                  <div className="text-lg">📋</div>
                </div>
              </div>
            )}
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{project.title}</h1>
              <div className="flex items-center space-x-2 mb-3">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)}`}>
                  {project.status}
                </span>
                <span className="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full">
                  {project.category}
                </span>
              </div>
              <p className="text-gray-600 text-lg">{project.description}</p>
            </div>
          </div>
        </div>

        {/* Project Stats */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <FaUsers className="text-blue-600" />
              <div>
                <p className="text-sm text-blue-600">Applications</p>
                <p className="text-xl font-bold text-blue-900">
                  {project.applicationCount}/{project.maxInterns}
                </p>
                {/* <p className="text-xs text-blue-700">
                  {calculateFillPercentage(project.applicationCount, project.maxInterns)}% filled
                </p> */}
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <FaClock className="text-green-600" />
              <div>
                <p className="text-sm text-green-600">Duration</p>
                <p className="text-lg font-bold text-green-900">{project.duration}</p>
              </div>
            </div>
          </div>

          <div className="bg-orange-50 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <FaQuestionCircle className="text-orange-600" />
              <div>
                <p className="text-sm text-orange-600">Prerequisites</p>
                <p className="text-lg font-bold text-orange-900">{project.prerequisiteQuestions.length}</p>
                <p className="text-xs text-orange-700">questions</p>
              </div>
            </div>
          </div>
        </div>

        {/* Technologies */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Technologies</h3>
          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
              >
                {tech}
              </span>
            ))}
          </div>
        </div>

        {/* Requirements */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Requirements</h3>
          <ul className="space-y-2">
            {project.requirements.map((req, index) => (
              <li key={index} className="flex items-start space-x-2">
                <FaCheckCircle className="text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700">{req}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Benefits */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Benefits</h3>
          <ul className="space-y-2">
            {project.gains.map((benefit, index) => (
              <li key={index} className="flex items-start space-x-2">
                <FaCheckCircle className="text-blue-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700">{benefit}</span>
              </li>
            ))}
          </ul>
        </div>
        {/* Prerequisite Questions */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Prerequisite Questions</h3>
          <div className="space-y-4">
            {project.prerequisiteQuestions.map((question, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Question {index + 1}</h4>
                  <div className="flex items-center space-x-2">
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {getQuestionTypeInternshipel(question.questionType)}
                    </span>
                    {question.required && (
                      <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                        Required
                      </span>
                    )}
                  </div>
                </div>
                <p className="text-gray-700 mb-2">{question.text}</p>
                {question.options && question.options.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium text-gray-600 mb-1">Options:</p>
                    <div className="flex flex-wrap gap-1">
                      {question.options.map((option, optIndex) => (
                        <span
                          key={optIndex}
                          className="px-2 py-1 bg-white text-gray-700 text-xs rounded border"
                        >
                          {option}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ProjectViewModal;
