import React, { useState, useEffect } from 'react';
import useAdminProjects from '../../hooks/useAdminProjects';
import {
  ProjectFormModal,
  DeleteProjectModal,
  ProjectViewModal,
  ProjectsHeader,
  ProjectsStats,
  ProjectsGrid,
  ProjectsFilter,
  LoadingState,
  ErrorState
} from '../../components/admin-projects';
import { Project, ProjectFormData } from '../../types/projects';

const AdminProjects: React.FC = () => {
  const {
    projects,
    loading,
    error,
    stats,
    filters,
    addProject,
    updateProject,
    deleteProject,
    getProjectById,
    refreshProjects,
    updateFilters,
    applyFilters,
    resetFilters
  } = useAdminProjects();

  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  // Load full project details when viewing/editing
  useEffect(() => {
    const loadProjectDetails = async () => {
      if ((isViewModalOpen || isEditModalOpen) && selectedProject?.id) {
        try {
          const fullProject = await getProjectById(selectedProject.id);
          if (fullProject) {
            setSelectedProject(fullProject);
          }
        } catch (err) {
          console.error('Error loading project details:', err);
        }
      }
    };

    loadProjectDetails();
  }, [isViewModalOpen, isEditModalOpen, selectedProject?.id]);

  // Handler functions
  const handleAddProject = () => {
    setSelectedProject(null);
    setIsAddModalOpen(true);
  };

  const handleEditProject = async (project: Project) => {
    setSelectedProject(project);
    setIsEditModalOpen(true);
  };

  const handleViewProject = async (project: Project) => {
    setSelectedProject(project);
    setIsViewModalOpen(true);
  };

  const handleDeleteProject = (project: Project) => {
    setSelectedProject(project);
    setIsDeleteModalOpen(true);
  };

  const handleAddSubmit = async (projectData: ProjectFormData) => {
    const result = await addProject(projectData);
    if (result.success) {
      setIsAddModalOpen(false);
      await refreshProjects();
    }
    return result;
  };

  const handleEditSubmit = async (projectData: ProjectFormData) => {
    if (!selectedProject) return { success: false, error: 'No project selected' };
    const result = await updateProject(selectedProject.id, projectData);
    if (result.success) {
      setIsEditModalOpen(false);
      await refreshProjects();
    }
    return result;
  };

  const handleDeleteConfirm = async (projectId: string) => {
    const result = await deleteProject(projectId);
    if (result.success) {
      setIsDeleteModalOpen(false);
      await refreshProjects();
    }
    return result;
  };

  const handleCloseModals = () => {
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsViewModalOpen(false);
    setIsDeleteModalOpen(false);
    setSelectedProject(null);
  };

  if (loading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={refreshProjects} />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <ProjectsHeader onAddProject={handleAddProject} />

      {/* Stats Cards */}
      <ProjectsStats stats={stats} />

      {/* Filter Component */}
      <ProjectsFilter
        filters={filters}
        onFiltersChange={updateFilters}
        onApplyFilters={applyFilters}
        onResetFilters={resetFilters}
        loading={loading}
      />

      {/* Projects Grid */}
      <ProjectsGrid
        projects={projects}
        onViewProject={handleViewProject}
        onEditProject={handleEditProject}
        onDeleteProject={handleDeleteProject}
        onAddProject={handleAddProject}
      />

      {/* Modals */}
      {/* Add Project Modal */}
      <ProjectFormModal
        isOpen={isAddModalOpen}
        onClose={handleCloseModals}
        onSubmit={handleAddSubmit}
        title="Add New Project"
      />

      {/* Edit Project Modal */}
      <ProjectFormModal
        isOpen={isEditModalOpen}
        onClose={handleCloseModals}
        onSubmit={handleEditSubmit}
        project={selectedProject}
        title="Edit Project"
        // isLoading={isLoadingProject}
      />

      {/* View Project Modal */}
      <ProjectViewModal
        isOpen={isViewModalOpen}
        onClose={handleCloseModals}
        project={selectedProject}
        // isLoading={isLoadingProject}
      />

      {/* Delete Project Modal */}
      <DeleteProjectModal
        isOpen={isDeleteModalOpen}
        onClose={handleCloseModals}
        onConfirm={handleDeleteConfirm}
        project={selectedProject}
      />
    </div>
  );
};

export default AdminProjects;