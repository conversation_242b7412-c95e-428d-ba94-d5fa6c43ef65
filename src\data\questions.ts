import { Question } from '../types';

export const questions: Question[] = [
  // E-commerce Platform Development (Project ID: 1)
  {
    id: 'q1-1',
    projectId: '1',
    question: 'How would you rate your experience with React?',
    type: 'rating',
    required: true,
    order: 1
  },
  {
    id: 'q1-2',
    projectId: '1',
    question: 'Have you worked with Node.js before?',
    type: 'yes-no',
    required: true,
    order: 2
  },
  {
    id: 'q1-3',
    projectId: '1',
    question: 'Which databases have you worked with?',
    type: 'multiple-choice',
    options: ['MongoDB', 'PostgreSQL', 'MySQL', 'SQLite', 'None'],
    required: true,
    order: 3
  },
  {
    id: 'q1-4',
    projectId: '1',
    question: 'Describe a challenging project you have worked on and how you solved it.',
    type: 'text',
    required: true,
    order: 4
  },

  // Mobile App for Task Management (Project ID: 2)
  {
    id: 'q2-1',
    projectId: '2',
    question: 'Have you developed mobile applications before?',
    type: 'yes-no',
    required: true,
    order: 1
  },
  {
    id: 'q2-2',
    projectId: '2',
    question: 'Which mobile development frameworks are you familiar with?',
    type: 'multiple-choice',
    options: ['React Native', 'Flutter', 'Native iOS', 'Native Android', 'None'],
    required: true,
    order: 2
  },
  {
    id: 'q2-3',
    projectId: '2',
    question: 'Rate your TypeScript knowledge level',
    type: 'rating',
    required: true,
    order: 3
  },
  {
    id: 'q2-4',
    projectId: '2',
    question: 'What interests you most about mobile app development?',
    type: 'text',
    required: true,
    order: 4
  },

  // AI-Powered Chatbot Development (Project ID: 3)
  {
    id: 'q3-1',
    projectId: '3',
    question: 'How comfortable are you with Python programming?',
    type: 'rating',
    required: true,
    order: 1
  },
  {
    id: 'q3-2',
    projectId: '3',
    question: 'Have you worked with machine learning libraries before?',
    type: 'yes-no',
    required: true,
    order: 2
  },
  {
    id: 'q3-3',
    projectId: '3',
    question: 'Which AI/ML concepts are you familiar with?',
    type: 'multiple-choice',
    options: ['Natural Language Processing', 'Deep Learning', 'Computer Vision', 'Reinforcement Learning', 'None'],
    required: true,
    order: 3
  },
  {
    id: 'q3-4',
    projectId: '3',
    question: 'Describe your experience with APIs and web services.',
    type: 'text',
    required: true,
    order: 4
  },

  // DevOps Pipeline Automation (Project ID: 4)
  {
    id: 'q4-1',
    projectId: '4',
    question: 'How comfortable are you with Linux command line?',
    type: 'rating',
    required: true,
    order: 1
  },
  {
    id: 'q4-2',
    projectId: '4',
    question: 'Have you used Docker before?',
    type: 'yes-no',
    required: true,
    order: 2
  },
  {
    id: 'q4-3',
    projectId: '4',
    question: 'Which cloud platforms have you worked with?',
    type: 'multiple-choice',
    options: ['AWS', 'Google Cloud', 'Azure', 'DigitalOcean', 'None'],
    required: true,
    order: 3
  },
  {
    id: 'q4-4',
    projectId: '4',
    question: 'What motivates you to work in DevOps and automation?',
    type: 'text',
    required: true,
    order: 4
  },

  // Frontend Dashboard for Analytics (Project ID: 5)
  {
    id: 'q5-1',
    projectId: '5',
    question: 'Rate your JavaScript proficiency',
    type: 'rating',
    required: true,
    order: 1
  },
  {
    id: 'q5-2',
    projectId: '5',
    question: 'Have you created data visualizations before?',
    type: 'yes-no',
    required: true,
    order: 2
  },
  {
    id: 'q5-3',
    projectId: '5',
    question: 'Which visualization libraries have you used?',
    type: 'multiple-choice',
    options: ['D3.js', 'Chart.js', 'Recharts', 'Plotly', 'None'],
    required: true,
    order: 3
  },
  {
    id: 'q5-4',
    projectId: '5',
    question: 'Describe your approach to creating user-friendly interfaces.',
    type: 'text',
    required: true,
    order: 4
  },

  // Backend API Development (Project ID: 6)
  {
    id: 'q6-1',
    projectId: '6',
    question: 'Which backend technologies are you most comfortable with?',
    type: 'multiple-choice',
    options: ['Node.js', 'Python', 'Java', 'C#', 'PHP'],
    required: true,
    order: 1
  },
  {
    id: 'q6-2',
    projectId: '6',
    question: 'Have you designed RESTful APIs before?',
    type: 'yes-no',
    required: true,
    order: 2
  },
  {
    id: 'q6-3',
    projectId: '6',
    question: 'Rate your database design knowledge',
    type: 'rating',
    required: true,
    order: 3
  },
  {
    id: 'q6-4',
    projectId: '6',
    question: 'Explain the importance of API security and how you would implement it.',
    type: 'text',
    required: true,
    order: 4
  }
];
