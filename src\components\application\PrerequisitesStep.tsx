import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent } from '../ui';
import { Project } from '../../hooks';

interface PrerequisitesStepProps {
  project: Project;
  onComplete: (data: Record<string, any>) => void;
  onBack: () => void;
}

// Import the PrerequisiteQuestion type from the project hook
import { PrerequisiteQuestion } from '../../types/projects';

interface ChatMessage {
  id: string;
  type: 'bot' | 'user';
  content: string;
  timestamp: Date;
  questionId?: string;
}

const PrerequisitesStep: React.FC<PrerequisitesStepProps> = ({ project, onComplete, onBack }) => {
  const [questions] = useState<PrerequisiteQuestion[]>(project.prerequisiteQuestions);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [showInput, setShowInput] = useState(false);
  const [currentInput, setCurrentInput] = useState('');
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  console.log(project)
  const scrollToBottom = () => {
    const chatContainer = document.getElementById('chat-messages');
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Initial greeting
    const initialMessage: ChatMessage = {
      id: 'greeting',
      type: 'bot',
      content: `Hi! 👋 I'm here to help you apply for the "${project.title}" internship. I'll ask you a few questions to make sure you're a good fit for this role. Let's get started!`,
      timestamp: new Date()
    };
    setMessages([initialMessage]);
    setShowInput(false); // Ensure input is hidden initially

    // Ask first question after a delay
    setTimeout(() => {
      askQuestion(0);
    }, 1500);
  }, []);

  const askQuestion = (questionIndex: number) => {
    if (questionIndex >= questions.length) {
      // All questions answered
      setShowInput(false);
      setTimeout(() => {
        const completionMessage: ChatMessage = {
          id: 'completion',
          type: 'bot',
          content: '🎉 Great! You\'ve answered all the prerequisite questions. You seem like a good fit for this role. Let\'s move on to the next step where you\'ll provide your detailed information.',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, completionMessage]);

        setTimeout(() => {
          onComplete(answers);
        }, 2000);
      }, 1000);
      return;
    }

    setIsTyping(true);
    setShowInput(false); // Hide input while typing

    setTimeout(() => {
      const question = questions[questionIndex];
      const questionMessage: ChatMessage = {
        id: `question-${questionIndex}`,
        type: 'bot',
        content: question.text,
        timestamp: new Date(),
        questionId: question.id || `question-${currentQuestionIndex}`
      };
      setMessages(prev => [...prev, questionMessage]);
      setIsTyping(false);

      // Show input after question is displayed
      setTimeout(() => {
        setShowInput(true);
      }, 500);
    }, 1000);
  };

  const handleAnswer = (answer: any) => {
    const question = questions[currentQuestionIndex];
    const userMessage: ChatMessage = {
      id: `answer-${currentQuestionIndex}`,
      type: 'user',
      content: formatAnswerForDisplay(answer, question),
      timestamp: new Date()
    };

    // Update answers with the current answer
    const updatedAnswers = {
      ...answers,
      [question.id || `question-${currentQuestionIndex}`]: answer
    };
    setAnswers(updatedAnswers);

    setMessages(prev => [...prev, userMessage]);
    setCurrentInput('');
    setSelectedOptions([]);
    setShowInput(false); // Hide input immediately after answer

    // Check if this is the last question
    const isLastQuestion = currentQuestionIndex === questions.length - 1;

    if (isLastQuestion) {
      // Add a completion message
      const completionMessage: ChatMessage = {
        id: 'completion',
        type: 'bot',
        content: 'Thank you for answering all the questions! We\'ll now proceed to the next step.',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, completionMessage]);

      // Call onComplete with the updated answers after a short delay
      setTimeout(() => {
        onComplete(updatedAnswers);
      }, 1500);
    } else {
      // Not the last question, increment index and ask the next question
      setCurrentQuestionIndex(prev => prev + 1);

      // Ask next question
      setTimeout(() => {
        askQuestion(currentQuestionIndex + 1);
      }, 1000);
    }
  };

  const formatAnswerForDisplay = (answer: any, question: PrerequisiteQuestion): string => {
    const type = question.questionType;
    if (type === 'BOOLEAN') {
      return answer ? 'Yes' : 'No';
    }
    if (type === 'MULTISELECT' && Array.isArray(answer)) {
      return answer.join(', ');
    }
    return String(answer);
  };

  const renderQuestionInput = () => {
    if (currentQuestionIndex >= questions.length || isTyping || !showInput) return null;

    const question = questions[currentQuestionIndex];

    const type = question.questionType;
    switch (type) {
      case 'TEXT':
        return (
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            <input
              type="text"
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && currentInput.trim() && handleAnswer(currentInput.trim())}
              placeholder="Type your answer..."
              className="flex-1 px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm sm:text-base"
              autoFocus
            />
            <Button
              onClick={() => handleAnswer(currentInput.trim())}
              disabled={!currentInput.trim()}
              variant="gradient"
              className="w-full sm:w-auto"
            >
              Send
            </Button>
          </div>
        );

      case 'SELECT':
        return (
          <div className="space-y-2">
            {question.options?.map((option, index) => (
              <button
                key={index}
                onClick={() => handleAnswer(option)}
                className="w-full text-left px-3 sm:px-4 py-2 sm:py-3 bg-white border border-gray-300 rounded-xl hover:bg-purple-50 hover:border-purple-300 transition-all duration-200 text-sm sm:text-base"
              >
                {option}
              </button>
            ))}
          </div>
        );

      case 'MULTISELECT':
        return (
          <div className="space-y-3">
            <div className="space-y-2">
              {question.options?.map((option, index) => (
                <label
                  key={index}
                  className="flex items-center space-x-3 px-3 sm:px-4 py-2 sm:py-3 bg-white border border-gray-300 rounded-xl hover:bg-purple-50 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={selectedOptions.includes(option)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedOptions(prev => [...prev, option]);
                      } else {
                        setSelectedOptions(prev => prev.filter(o => o !== option));
                      }
                    }}
                    className="w-4 h-4 text-purple-600 rounded focus:ring-purple-500 flex-shrink-0"
                  />
                  <span className="text-sm sm:text-base">{option}</span>
                </label>
              ))}
            </div>
            <Button
              onClick={() => handleAnswer(selectedOptions)}
              disabled={selectedOptions.length === 0}
              variant="gradient"
              className="w-full"
            >
              Continue
            </Button>
          </div>
        );

      case 'BOOLEAN':
        return (
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <Button
              onClick={() => handleAnswer(true)}
              variant="gradient"
              className="flex-1"
            >
              Yes
            </Button>
            <Button
              onClick={() => handleAnswer(false)}
              variant="outline"
              className="flex-1"
            >
              No
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card className="w-full" gradient>
        <CardContent className="p-0">
          {/* Chat Header */}
          <div className="p-4 sm:p-6 border-b border-white/20">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white font-bold">🤖</span>
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-bold text-gray-900 text-sm sm:text-base">MSS Internship Assistant</h3>
                <p className="text-xs sm:text-sm text-gray-600">Prerequisites Check</p>
              </div>
            </div>
          </div>

          {/* Chat Messages */}
          <div className="h-[60vh] sm:h-96 overflow-y-auto p-4 sm:p-6 space-y-4" id="chat-messages">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] sm:max-w-xs lg:max-w-md px-3 sm:px-4 py-2 sm:py-3 rounded-2xl ${
                    message.type === 'user'
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                      : 'bg-white border border-gray-200 text-gray-900'
                  }`}
                >
                  <p className="text-sm sm:text-base leading-relaxed">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.type === 'user' ? 'text-purple-100' : 'text-gray-500'
                  }`}>
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </p>
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-white border border-gray-200 text-gray-900 px-3 sm:px-4 py-2 sm:py-3 rounded-2xl">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="p-4 sm:p-6 border-t border-white/20">
            {renderQuestionInput()}

            {currentQuestionIndex >= questions.length && !isTyping && !showInput && (
              <div className="flex space-x-3">
                <Button onClick={onBack} variant="outline" className="flex-1">
                  Back to Project
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PrerequisitesStep;
