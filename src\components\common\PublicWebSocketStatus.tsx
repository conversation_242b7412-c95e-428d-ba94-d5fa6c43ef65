import React, { useContext } from 'react';
import PublicWebSocketContext, { usePublicWebSocket } from '../../contexts/PublicWebSocketContext';
import { FaWifi, FaExclamationTriangle, FaSpinner } from 'react-icons/fa';

const PublicWebSocketStatus: React.FC = () => {
  // Check if context is available first
  const context = useContext(PublicWebSocketContext);

  // If context is not available, don't render anything
  if (!context) {
    return null;
  }

  // Now safely use the hook
  const { connectionStatus, reconnect } = usePublicWebSocket();

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <FaWifi className="text-green-500" />;
      case 'connecting':
        return <FaSpinner className="text-yellow-500 animate-spin" />;
      case 'error':
      case 'disconnected':
        return <FaExclamationTriangle className="text-red-500" />;
      default:
        return <FaWifi className="text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Live Updates';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Connection Error';
      case 'disconnected':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'connecting':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error':
      case 'disconnected':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Only show status indicator if there's an issue or connecting
  if (connectionStatus === 'connected') {
    return null; // Don't show anything when connected (clean UI)
  }

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor()}`}>
      {getStatusIcon()}
      <span className="ml-1">{getStatusText()}</span>
      {(connectionStatus === 'error' || connectionStatus === 'disconnected') && (
        <button
          onClick={reconnect}
          className="ml-2 text-xs underline hover:no-underline"
        >
          Retry
        </button>
      )}
    </div>
  );
};

export default PublicWebSocketStatus;
