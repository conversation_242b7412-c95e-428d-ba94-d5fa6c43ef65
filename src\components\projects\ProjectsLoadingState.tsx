import React from 'react';
import { <PERSON><PERSON>, Footer } from '../layout';
import { LoadingSpinner } from '../ui';

const ProjectsLoadingState: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50 flex flex-col">
      <Header />
      <div className="flex-1 flex items-center justify-center px-4 py-20">
        <div className="text-center w-full max-w-lg">
          <div className="flex justify-center mb-8">
            <LoadingSpinner size="xl" color="primary" />
          </div>
          <div className="space-y-6">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold gradient-text-primary">
              Loading Projects...
            </h2>
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-md mx-auto">
              Discovering amazing opportunities for your career growth
            </p>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ProjectsLoadingState;
