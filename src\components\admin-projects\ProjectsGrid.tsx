import React from 'react';
import { Project } from '../../types/projects';
import ProjectCard from './ProjectCard';
import EmptyState from './EmptyState';

interface ProjectsGridProps {
  projects: Project[];
  onViewProject: (project: Project) => void;
  onEditProject: (project: Project) => void;
  onDeleteProject: (project: Project) => void;
  onAddProject: () => void;
}

const ProjectsGrid: React.FC<ProjectsGridProps> = ({
  projects,
  onViewProject,
  onEditProject,
  onDeleteProject,
  onAddProject
}) => {
  if (!projects || projects.length === 0) {
    return <EmptyState onAddProject={onAddProject} />;
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      {projects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
          onView={onViewProject}
          onEdit={onEditProject}
          onDelete={onDeleteProject}
        />
      ))}
    </div>
  );
};

export default ProjectsGrid;
