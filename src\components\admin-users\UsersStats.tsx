import React from 'react';
import { FaUsers, FaUserTie, FaUserShield } from 'react-icons/fa';

interface UserStats {
  totalUsers: number;
  activeCount: number;
  internCount: number;
  managerCount: number;
  teamLeaderCount: number;
  adminCount: number;
  inactiveCount: number;
}

interface UsersStatsProps {
  stats: UserStats;
}

const UsersStats: React.FC<UsersStatsProps> = ({ stats }) => {
  const statItems = [
    {
      id: 'total',
      value: stats.totalUsers,
      label: 'Total',
      icon: <FaUsers className="text-blue-600" />,
      bgColor: 'bg-blue-100'
    },
    {
      id: 'active',
      value: stats.activeCount,
      label: 'Active',
      icon: <div className="w-3 h-3 bg-green-500 rounded-full"></div>,
      bgColor: 'bg-green-100'
    },
    {
      id: 'interns',
      value: stats.internCount,
      label: 'Interns',
      icon: <FaUsers className="text-purple-600" />,
      bgColor: 'bg-purple-100'
    },
    {
      id: 'teamLeaders',
      value: stats.teamLeaderCount,
      label: 'Team Leaders',
      icon: <FaUserTie className="text-indigo-600" />,
      bgColor: 'bg-indigo-100'
    },
    {
      id: 'managers',
      value: stats.managerCount,
      label: 'Managers',
      icon: <FaUserShield className="text-emerald-600" />,
      bgColor: 'bg-emerald-100'
    },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
      {statItems.map((item) => (
        <div key={item.id} className="bg-white rounded-xl p-4 shadow-sm border border-gray-200">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 ${item.bgColor} rounded-lg flex items-center justify-center`}>
              {item.icon}
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">{item.value}</p>
              <p className="text-sm text-gray-600">{item.label}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default UsersStats;
