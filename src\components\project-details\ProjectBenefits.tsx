import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui';
import { FaGift } from 'react-icons/fa';
import { Project } from '../../types/projects';

interface ProjectBenefitsProps {
  project: Project;
}

const ProjectBenefits: React.FC<ProjectBenefitsProps> = ({ project }) => {
  return (
    <Card gradient>
      <CardHeader>
        <h2 className="text-xl lg:text-2xl font-bold text-gray-900 flex items-center">
          <FaGift className="mr-2 lg:mr-3 text-lg lg:text-xl text-gray-600" />
          What You'll Gain
        </h2>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2 lg:space-y-3">
          {project.gains.map((benefit, index) => (
            <li key={index} className="flex items-start space-x-2 lg:space-x-3">
              <span className="text-purple-500 mt-0.5 lg:mt-1 text-sm lg:text-base">★</span>
              <span className="text-gray-700 text-sm lg:text-base">{benefit}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};

export default ProjectBenefits;
