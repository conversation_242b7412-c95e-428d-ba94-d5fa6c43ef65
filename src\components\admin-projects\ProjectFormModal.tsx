import React, { useState, useEffect, useRef } from 'react';
import { Modal, Button, Input} from '../ui';
import { FaTimes, FaPlus, FaTrash} from 'react-icons/fa';
import { Upload, X } from 'lucide-react';
import { Project, PrerequisiteQuestion, ProjectFormData } from '../../types/projects';


interface ProjectFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (projectData: ProjectFormData) => Promise<{ success: boolean; error?: string }>;
  project?: Project | null;
  title: string;
}

const ProjectFormModal: React.FC<ProjectFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  project,
  title
}) => {
  const [formData, setFormData] = useState<ProjectFormData>({
    title: '',
    description: '',
    technologies: [] as string[],
    duration: '',
    category: '',
    requirements: [] as string[],
    gains: [] as string[],
    maxInterns: 1,
    status: 'ACTIVE' as 'ACTIVE' | 'INACTIVE' | 'CLOSED',
    prerequisiteQuestions: [] as PrerequisiteQuestion[],
    image: null,
    deleteImage: false
  });

  const [techInput, setTechInput] = useState('');
  const [requirementInput, setRequirementInput] = useState('');
  const [benefitInput, setBenefitInput] = useState('');
  const [optionInputs, setOptionInputs] = useState<Record<number, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [dragActive, setDragActive] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [serverImage, setServerImage] = useState<string | undefined>(undefined);
  const fileInputRef = useRef<HTMLInputElement>(null);


  // Reset form when modal opens/closes or project changes
  useEffect(() => {
    if (isOpen) {
      if (project) {
        setFormData({
          title: project.title || '',
          description: project.description || '',
          technologies: project.technologies || [],
          duration: project.duration || '',
          category: project.category || '',
          requirements: project.requirements || [],
          gains: project.gains || [],
          maxInterns: project.maxInterns || 1,
          status: project.status || 'ACTIVE',
          prerequisiteQuestions: project.prerequisiteQuestions || [],
          image: null,
          deleteImage: false
        });
        // Set existing image as preview if available and not empty
        setImagePreview(project.image && project.image !== "" ? project.image : null);
      } else {
        setFormData({
          title: '',
          description: '',
          technologies: [],
          duration: '',
          category: '',
          requirements: [],
          gains: [],
          maxInterns: 1,
          status: 'ACTIVE',
          image: null,
          prerequisiteQuestions: [
            {
              text: 'What is your current programming experience level?',
              questionType: 'SELECT',
              options: ['Beginner (0-1 years)', 'Intermediate (1-3 years)', 'Advanced (3+ years)'],
              required: true
            },
            {
              text: 'How many hours per week can you commit to this internship?',
              questionType: 'TEXT',
              options: [],
              required: true
            },
            {
              text: 'Are you comfortable working remotely?',
              questionType: 'BOOLEAN',
              options: [],
              required: true
            },
            {
              text: 'When can you start the internship?',
              questionType: 'SELECT',
              options: ['Immediately', 'Within 2 weeks', 'Within 1 month', 'Within 2 months'],
              required: true
            }
          ],
          deleteImage:false
        });
      }
      setTechInput('');
      setRequirementInput('');
      setBenefitInput('');
      setOptionInputs({});
      setErrors({});
      setImagePreview(null);
      setServerImage(project?.image);
      console.log("image",project?.image)
    }
  }, [isOpen, project]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Basic field validation
    if (!formData.title.trim()) newErrors.title = 'Project title is required';
    if (!formData.description.trim()) newErrors.description = 'Project description is required';
    if (!formData.category.trim()) newErrors.category = 'Project category is required';
    if (!formData.duration.trim()) newErrors.duration = 'Project duration is required';
    if (formData.maxInterns < 1) newErrors.maxInterns = 'Maximum interns must be at least 1';

    // Array field validation
    if (formData.technologies.length === 0) newErrors.technologies = 'At least one technology is required';
    if (formData.requirements.length === 0) newErrors.requirements = 'At least one requirement is required';
    if (formData.gains.length === 0) newErrors.gains = 'At least one benefit/gain is required';
    if (formData.prerequisiteQuestions.length === 0) newErrors.prerequisiteQuestions = 'At least one prerequisite question is required';

    // Validate prerequisite questions
    formData.prerequisiteQuestions.forEach((question, index) => {
      if (!question.text.trim()) {
        newErrors[`question_${index}_text`] = `Question ${index + 1} text is required`;
      }

      if ((question.questionType === 'SELECT' || question.questionType === 'MULTISELECT') &&
          (!question.options || question.options.length === 0)) {
        newErrors[`question_${index}_options`] = `Question ${index + 1} must have at least one option`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Validate form
    if (!validateForm()) {
      setIsLoading(false);
      return;
    }

    const result = await onSubmit(formData);

    if (result.success) {
      onClose();
    } else {
      // Set a general error if submission fails
      setErrors({ general: result.error || 'Failed to save project' });
    }

    setIsLoading(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) || 0 : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const addTechnology = () => {
    if (techInput.trim() && !formData.technologies.includes(techInput.trim())) {
      setFormData(prev => ({
        ...prev,
        technologies: [...prev.technologies, techInput.trim()]
      }));
      setTechInput('');
    }
  };

  const removeTechnology = (techToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      technologies: prev.technologies.filter(tech => tech !== techToRemove)
    }));
  };

  const addRequirement = () => {
    if (requirementInput.trim() && !formData.requirements.includes(requirementInput.trim())) {
      setFormData(prev => ({
        ...prev,
        requirements: [...prev.requirements, requirementInput.trim()]
      }));
      setRequirementInput('');
    }
  };

  const removeRequirement = (reqToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.filter(req => req !== reqToRemove)
    }));
  };

  const addBenefit = () => {
    if (benefitInput.trim() && !formData.gains.includes(benefitInput.trim())) {
      setFormData(prev => ({
        ...prev,
        gains: [...prev.gains, benefitInput.trim()]
      }));
      setBenefitInput('');
    }
  };

  const removeBenefit = (gainToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      gains: prev.gains.filter(gain => gain !== gainToRemove)
    }));
  };

  // File upload handlers
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      validateAndSelectFile(file);
    }
  };

  const validateAndSelectFile = (file: File) => {
    // Validate file type - Only PNG and JPG allowed
    if (!['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {
      setErrors(prev => ({ ...prev, image: 'Please upload PNG or JPG images only' }));
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, image: 'File size must be less than 5MB' }));
      return;
    }

    // Create image preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    setFormData(prev => ({ ...prev, image: file, deleteImage:false }));
    setErrors(prev => ({ ...prev, image: '' }));
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      validateAndSelectFile(file);
    }
  };

  const removeImage = () => {
    setFormData(prev => ({ ...prev, image: null, deleteImage: true }));
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setServerImage(undefined)
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const addPrerequisiteQuestion = () => {
    const newQuestion: PrerequisiteQuestion = {
      text: '',
      questionType: 'TEXT',
      required: true,
      options: []
    };
    setFormData(prev => ({
      ...prev,
      prerequisiteQuestions: [...prev.prerequisiteQuestions, newQuestion]
    }));
  };

  const updatePrerequisiteQuestion = (index: number, field: keyof PrerequisiteQuestion, value: any) => {
    setFormData(prev => ({
      ...prev,
      prerequisiteQuestions: prev.prerequisiteQuestions.map((q, i) => 
        i === index ? { ...q, [field]: value } : q
      )
    }));
  };

  const removePrerequisiteQuestion = (index: number) => {
    setFormData(prev => ({
      ...prev,
      prerequisiteQuestions: prev.prerequisiteQuestions.filter((_, i) => i !== index)
    }));
  };

  const addOptionToQuestion = (questionIndex: number) => {
    const currentInput = optionInputs[questionIndex] || '';
    if (!currentInput.trim()) return;

    setFormData(prev => ({
      ...prev,
      prerequisiteQuestions: prev.prerequisiteQuestions.map((q, i) =>
        i === questionIndex
          ? { ...q, options: [...(q.options || []), currentInput.trim()] }
          : q
      )
    }));

    // Clear the input for this specific question
    setOptionInputs(prev => ({ ...prev, [questionIndex]: '' }));
  };

  const removeOptionFromQuestion = (questionIndex: number, optionToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      prerequisiteQuestions: prev.prerequisiteQuestions.map((q, i) =>
        i === questionIndex
          ? { ...q, options: (q.options || []).filter(option => option !== optionToRemove) }
          : q
      )
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      action();
    }
  };



  return (
    <Modal title={title} isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6 max-h-[90vh] overflow-y-auto">
    

        {/* General Error Message */}
        {errors.general && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">{errors.general}</p>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Project Title *
                </label>
                <Input
                  id="title"
                  name="title"
                  type="text"
                  required
                  value={formData.title}
                  onChange={handleChange}
                  error={errors.title}
                  placeholder="Enter project title"
                  disabled={isLoading}
                />
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <Input
                  id="category"
                  name="category"
                  type="text"
                  required
                  value={formData.category}
                  onChange={handleChange}
                  error={errors.category}
                  placeholder="e.g., Web Development, AI/ML"
                  disabled={isLoading}
                />
              </div>
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                id="description"
                name="description"
                rows={4}
                required
                value={formData.description}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter project description"
                disabled={isLoading}
              />
              {errors.description && <p className="text-sm text-red-600 mt-1">{errors.description}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        
              <div>
                <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-2">
                  Duration *
                </label>
                <Input
                  id="duration"
                  name="duration"
                  type="text"
                  required
                  value={formData.duration}
                  onChange={handleChange}
                  error={errors.duration}
                  placeholder="e.g., 3 months"
                  disabled={isLoading}
                />
              </div>

              <div>
                <label htmlFor="maxInterns" className="block text-sm font-medium text-gray-700 mb-2">
                  Max Interns *
                </label>
                <Input
                  id="maxInterns"
                  name="maxInterns"
                  type="number"
                  min="1"
                  required
                  value={formData.maxInterns}
                  onChange={handleChange}
                  error={errors.maxInterns}
                  disabled={isLoading}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isLoading}
                >
                  <option value="ACTIVE">Active</option>
                  <option value="INACTIVE">InActive</option>
                  <option value="CLOSED">Closed</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Project Image *
              </label>

              {!formData.image && !imagePreview && !serverImage ? (
                <div
                  className={`relative border-2 border-dashed rounded-lg p-6 text-center hover:border-gray-400 transition-colors ${
                    dragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
                  } ${errors.image ? 'border-red-300 bg-red-50' : ''}`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".png,.jpg,.jpeg"
                    onChange={handleFileChange}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    disabled={isLoading}
                  />
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-600">
                    <span className="font-medium text-blue-600 hover:text-blue-500">
                      Click to upload
                    </span>{' '}
                    or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">PNG or JPG files up to 5MB</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {/* Image Preview */}
                  {(imagePreview!==null||serverImage!==undefined) && (
                    <div className="relative">
                      <img
                        src={imagePreview!==null ? imagePreview : serverImage }
                        alt="Project preview"
                        className="w-full h-48 object-cover rounded-lg border"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-40 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                        <div className="flex space-x-2">
                          <Button
                            type="button"
                            onClick={() => fileInputRef.current?.click()}
                            variant="outline"
                            size="sm"
                            disabled={isLoading}
                            className="bg-white text-gray-900 hover:bg-gray-100"
                          >
                            <Upload className="w-4 h-4 mr-2" />
                            Change
                          </Button>
                          <Button
                            type="button"
                            onClick={removeImage}
                            variant="outline"
                            size="sm"
                            disabled={isLoading}
                            className="bg-white text-gray-900 hover:bg-gray-100"
                          >
                            <X className="w-4 h-4 mr-2" />
                            Remove
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Hidden file input for changing existing image */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".png,.jpg,.jpeg"
                    onChange={handleFileChange}
                    className="hidden"
                    disabled={isLoading}
                  />

                  {/* File Info - Only show when we have a file selected */}
                  {formData.image && (
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Upload className="w-5 h-5 text-blue-600" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {formData.image.name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {formatFileSize(formData.image.size)}
                          </p>
                        </div>
                      </div>
                      <Button
                        type="button"
                        onClick={removeImage}
                        variant="outline"
                        size="sm"
                        disabled={isLoading}
                        className="ml-3"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  )}
                </div>
              )}

              {errors.image && (
                <p className="mt-1 text-sm text-red-600">{errors.image}</p>
              )}
            </div>
          </div>

          {/* Technologies */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Technologies *</h3>
            <div className="flex space-x-2">
              <Input
                type="text"
                value={techInput}
                onChange={(e) => setTechInput(e.target.value)}
                onKeyDown={(e) => handleKeyPress(e, addTechnology)}
                className="flex-1"
                placeholder="Add a technology..."
                disabled={isLoading}
              />
              <Button
                type="button"
                onClick={addTechnology}
                variant="outline"
                size="sm"
                disabled={isLoading || !techInput.trim()}
              >
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.technologies.map((tech, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                >
                  {tech}
                  <button
                    type="button"
                    onClick={() => removeTechnology(tech)}
                    className="ml-2 text-blue-600 hover:text-blue-800"
                    disabled={isLoading}
                  >
                    <FaTimes className="text-xs" />
                  </button>
                </span>
              ))}
            </div>
            {errors.technologies && <p className="text-sm text-red-600 mt-2">{errors.technologies}</p>}
          </div>

          {/* Requirements */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Requirements *</h3>
            <div className="flex space-x-2">
              <Input
                type="text"
                value={requirementInput}
                onChange={(e) => setRequirementInput(e.target.value)}
                onKeyDown={(e) => handleKeyPress(e, addRequirement)}
                className="flex-1"
                placeholder="Add a requirement..."
                disabled={isLoading}
              />
              <Button
                type="button"
                onClick={addRequirement}
                variant="outline"
                size="sm"
                disabled={isLoading || !requirementInput.trim()}
              >
                Add
              </Button>
            </div>
            <div className="space-y-1">
              {formData.requirements.map((req, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                >
                  <span className="text-sm text-gray-700">{req}</span>
                  <button
                    type="button"
                    onClick={() => removeRequirement(req)}
                    className="text-red-600 hover:text-red-800"
                    disabled={isLoading}
                  >
                    <FaTimes className="text-xs" />
                  </button>
                </div>
              ))}
            </div>
            {errors.requirements && <p className="text-sm text-red-600 mt-2">{errors.requirements}</p>}
          </div>

          {/* Benefits */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Benefits *</h3>
            <div className="flex space-x-2">
              <Input
                type="text"
                value={benefitInput}
                onChange={(e) => setBenefitInput(e.target.value)}
                onKeyDown={(e) => handleKeyPress(e, addBenefit)}
                className="flex-1"
                placeholder="Add a benefit..."
                disabled={isLoading}
              />
              <Button
                type="button"
                onClick={addBenefit}
                variant="outline"
                size="sm"
                disabled={isLoading || !benefitInput.trim()}
              >
                Add
              </Button>
            </div>
            <div className="space-y-1">
              {formData.gains.map((benefit, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                >
                  <span className="text-sm text-gray-700">{benefit}</span>
                  <button
                    type="button"
                    onClick={() => removeBenefit(benefit)}
                    className="text-red-600 hover:text-red-800"
                    disabled={isLoading}
                  >
                    <FaTimes className="text-xs" />
                  </button>
                </div>
              ))}
            </div>
            {errors.gains && <p className="text-sm text-red-600 mt-2">{errors.gains}</p>}
          </div>


          {/* Prerequisite Questions */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Prerequisite Questions *</h3>
              <Button
                type="button"
                onClick={addPrerequisiteQuestion}
                variant="outline"
                size="sm"
                className="flex items-center space-x-1"
                disabled={isLoading}
              >
                <FaPlus />
                <span>Add Question</span>
              </Button>
            </div>
            
            <div className="space-y-4">
              {formData.prerequisiteQuestions.map((question, index) => (
                <div key={index} className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-700">Question {index + 1}</h4>
                    <button
                      type="button"
                      onClick={() => removePrerequisiteQuestion(index)}
                      className="text-red-600 hover:text-red-800"
                      disabled={isLoading}
                    >
                      <FaTrash className="text-xs" />
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-600 mb-1">Question Text</label>
                      <Input
                        type="text"
                        value={question.text}
                        onChange={(e) => updatePrerequisiteQuestion(index, 'text', e.target.value)}
                        error={errors[`question_${index}_text`]}
                        placeholder="Enter question text"
                        disabled={isLoading}
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">Type</label>
                        <select
                          value={question.questionType}
                          onChange={(e) => updatePrerequisiteQuestion(index, 'questionType', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          disabled={isLoading}
                        >
                          <option value="TEXT">Text</option>
                          <option value="SELECT">Select</option>
                          <option value="MULTISELECT">Multi-select</option>
                          <option value="BOOLEAN">Yes/No</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="flex items-center space-x-2 text-xs font-medium text-gray-600">
                          <input
                            type="checkbox"
                            checked={question.required}
                            onChange={(e) => updatePrerequisiteQuestion(index, 'required', e.target.checked)}
                            disabled={isLoading}
                          />
                          <span>Required</span>
                        </label>
                      </div>
                    </div>
                    
                    {(question.questionType === 'SELECT' || question.questionType === 'MULTISELECT') && (
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">Options</label>
                        <div className="flex space-x-2 mb-2">
                          <Input
                            type="text"
                            value={optionInputs[index] || ''}
                            onChange={(e) => setOptionInputs(prev => ({ ...prev, [index]: e.target.value }))}
                            onKeyDown={(e) => handleKeyPress(e, () => addOptionToQuestion(index))}
                            className="flex-1"
                            placeholder="Add an option..."
                            disabled={isLoading}
                          />
                          <Button
                            type="button"
                            onClick={() => addOptionToQuestion(index)}
                            variant="outline"
                            size="sm"
                            disabled={isLoading || !(optionInputs[index] || '').trim()}
                          >
                            Add
                          </Button>
                        </div>
                        <div className="space-y-1">
                          {(question.options || []).map((option, optionIndex) => (
                            <div
                              key={optionIndex}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                            >
                              <span className="text-sm text-gray-700">{option}</span>
                              <button
                                type="button"
                                onClick={() => removeOptionFromQuestion(index, option)}
                                className="text-red-600 hover:text-red-800"
                                disabled={isLoading}
                              >
                                <FaTrash className="text-xs" />
                              </button>
                            </div>
                          ))}
                        </div>
                        {errors[`question_${index}_options`] && (
                          <p className="text-sm text-red-600 mt-1">{errors[`question_${index}_options`]}</p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
            {errors.prerequisiteQuestions && <p className="text-sm text-red-600 mt-2">{errors.prerequisiteQuestions}</p>}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <Button
              type="button"
              onClick={onClose}
              variant="outline"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="gradient"
              disabled={isLoading}
              glow
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Saving...</span>
                </div>
              ) : (
                project ? 'Update Project' : 'Create Project'
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default ProjectFormModal;
