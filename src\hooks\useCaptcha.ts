import { useState, useCallback, useRef } from 'react';
import captchaService from '../services/captchaService';
import { ReCaptchaRef } from '../components/ui/ReCaptcha';

interface UseCaptchaReturn {
  captchaToken: string | null;
  captchaError: string | null;
  isVerifying: boolean;
  isCaptchaEnabled: boolean;
  captchaRef: React.RefObject<ReCaptchaRef | null>;
  handleCaptchaVerify: (token: string) => Promise<boolean>;
  handleCaptchaError: (error: string) => void;
  handleCaptchaExpired: () => void;
  resetCaptcha: () => void;
}

export const useCaptcha = (): UseCaptchaReturn => {
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [captchaError, setCaptchaError] = useState<string | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const captchaRef = useRef<ReCaptchaRef | null>(null);

  const isCaptchaEnabled = captchaService.isCaptchaEnabled();

  const handleCaptchaVerify = useCallback(async (token: string): Promise<boolean> => {
    if (!token) {
      setCaptchaError('No captcha token provided');
      return false;
    }

    setIsVerifying(true);
    setCaptchaError(null);

    try {
      const result = await captchaService.verifyCaptcha(token);
      
      if (result.success) {
        setCaptchaToken(token);
        setCaptchaError(null);
        return true;
      } else {
        setCaptchaError(result.message || 'Captcha verification failed');
        setCaptchaToken(null);
        // Reset captcha on verification failure
        if (captchaRef.current) {
          captchaRef.current.reset();
        }
        return false;
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Captcha verification failed';
      setCaptchaError(errorMessage);
      setCaptchaToken(null);
      // Reset captcha on error
      if (captchaRef.current) {
        captchaRef.current.reset();
      }
      return false;
    } finally {
      setIsVerifying(false);
    }
  }, []);

  const handleCaptchaError = useCallback((error: string) => {
    setCaptchaError(error);
    setCaptchaToken(null);
    // Reset captcha on error
    if (captchaRef.current) {
      captchaRef.current.reset();
    }
  }, []);

  const handleCaptchaExpired = useCallback(() => {
    setCaptchaToken(null);
    setCaptchaError(null);
  }, []);

  const resetCaptcha = useCallback(() => {
    setCaptchaToken(null);
    setCaptchaError(null);
    setIsVerifying(false);
    // Reset the actual captcha widget
    if (captchaRef.current) {
      captchaRef.current.reset();
    }
  }, []);

  return {
    captchaToken,
    captchaError,
    isVerifying,
    isCaptchaEnabled,
    captchaRef,
    handleCaptchaVerify,
    handleCaptchaError,
    handleCaptchaExpired,
    resetCaptcha
  };
};

export default useCaptcha;
