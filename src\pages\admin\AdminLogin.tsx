import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardContent } from '../../components/ui';
import { LoginHeader, LoginForm } from '../../components/admin';
import useAuth from '../../hooks/useAuth';
import { testApiConnection, debugApiHeaders } from '../../utils/apiTest';

const AdminLogin: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [apiTestResult, setApiTestResult] = useState<string | null>(null);

  const from = location.state?.from?.pathname || '/admin/dashboard';

  // Debug API connection
  useEffect(() => {
    const nodeEnv = import.meta.env.VITE_NODE_ENV;
    const isDevelopmentOrTest = nodeEnv === 'development' || nodeEnv === 'test';
    if (isDevelopmentOrTest) {
      console.log('🔧 API Base URL:', import.meta.env.VITE_API_BASE_URL);
      debugApiHeaders();
    }
  }, []);

  const handleLoginSubmit = async (username: string, password: string) => {
    setIsLoading(true);
    setError('');
    setApiTestResult(null);

    try {
      // In development/test mode, run API test first
      const nodeEnv = import.meta.env.VITE_NODE_ENV;
      const isDevelopmentOrTest = nodeEnv === 'development' || nodeEnv === 'test';
      if (isDevelopmentOrTest) {
        console.log('🧪 Testing API connection before login...');
        const testResult = await testApiConnection();
        setApiTestResult(
          testResult.success
            ? '✅ API connection test successful'
            : `❌ API test failed: ${testResult.error}`
        );
      }

      // Proceed with normal login
      const result = await login(username, password);

      if (result.success) {
        navigate(from, { replace: true });
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Login failed');
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-indigo-900 to-pink-900 flex items-center justify-center p-4">
      {/* Background Animation */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-pink-500/20 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="relative w-full max-w-md">
        <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardContent className="p-8">
            {/* Header */}
            <LoginHeader />

            {/* API Test Result */}
            {apiTestResult && (
              <div className={`mb-4 p-3 rounded-lg text-sm ${apiTestResult.startsWith('✅') ? 'bg-green-50 text-green-700' : 'bg-yellow-50 text-yellow-700'}`}>
                {apiTestResult}
              </div>
            )}

            {/* Login Form */}
            <LoginForm
              onSubmit={handleLoginSubmit}
              isLoading={isLoading}
              error={error}
            />

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-sm text-gray-500">
                Protected by MSS Internship Security
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminLogin;
