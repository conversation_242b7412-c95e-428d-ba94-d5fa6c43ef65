import React from 'react';
import { Fa<PERSON>heck, FaCircle } from 'react-icons/fa';

export interface StepperStep {
  id: string;
  title: string;
  description?: string;
  icon?: React.ReactNode;
  status?: 'completed' | 'current' | 'upcoming';
}

interface StepperProps {
  steps: StepperStep[];
  currentStep: number;
  orientation?: 'horizontal' | 'vertical';
  variant?: 'default' | 'minimal' | 'numbered';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Stepper: React.FC<StepperProps> = ({
  steps,
  currentStep,
  orientation = 'horizontal',
  variant = 'default',
  size = 'md',
  className = ''
}) => {
  const getStepStatus = (index: number): 'completed' | 'current' | 'upcoming' => {
    if (index < currentStep) return 'completed';
    if (index === currentStep) return 'current';
    return 'upcoming';
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          circle: 'w-8 h-8 text-sm',
          text: 'text-xs',
          title: 'text-sm',
          connector: 'h-0.5'
        };
      case 'lg':
        return {
          circle: 'w-12 h-12 text-lg',
          text: 'text-base',
          title: 'text-lg',
          connector: 'h-1'
        };
      default:
        return {
          circle: 'w-10 h-10 text-base',
          text: 'text-sm',
          title: 'text-base',
          connector: 'h-0.5'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  const getStepClasses = (status: 'completed' | 'current' | 'upcoming') => {
    const baseClasses = `${sizeClasses.circle} rounded-full flex items-center justify-center font-semibold transition-all duration-300 relative z-10`;
    
    switch (status) {
      case 'completed':
        return `${baseClasses} bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg ring-4 ring-green-100`;
      case 'current':
        return `${baseClasses} bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg ring-4 ring-blue-100 animate-pulse`;
      case 'upcoming':
        return `${baseClasses} bg-gray-200 text-gray-500 border-2 border-gray-300`;
    }
  };

  const getTextClasses = (status: 'completed' | 'current' | 'upcoming') => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'current':
        return 'text-blue-600 font-semibold';
      case 'upcoming':
        return 'text-gray-500';
    }
  };

  const getConnectorClasses = (index: number) => {
    const baseClasses = `${sizeClasses.connector} transition-all duration-500`;
    const isCompleted = index < currentStep;
    
    return `${baseClasses} ${
      isCompleted 
        ? 'bg-gradient-to-r from-green-500 to-emerald-500' 
        : 'bg-gray-300'
    }`;
  };

  const renderStepContent = (step: StepperStep, index: number) => {
    const status = getStepStatus(index);
    const stepClasses = getStepClasses(status);
    const textClasses = getTextClasses(status);

    return (
      <>
        {/* Step Circle */}
        <div className={stepClasses}>
          {status === 'completed' ? (
            <FaCheck className="text-sm" />
          ) : variant === 'numbered' ? (
            <span>{index + 1}</span>
          ) : step.icon ? (
            step.icon
          ) : (
            <FaCircle className="text-xs" />
          )}
        </div>

        {/* Step Text */}
        <div className="mt-3 max-w-[120px] text-center">
          <div className={`${sizeClasses.title} font-medium ${textClasses}`}>
            {step.title}
          </div>
          {step.description && (
            <div className={`${sizeClasses.text} text-gray-500 mt-1`}>
              {step.description}
            </div>
          )}
        </div>
      </>
    );
  };

  const renderHorizontalStepper = () => (
    <div className={`flex items-start w-full ${className}`}>
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          {/* Step Content */}
          <div className="flex flex-col items-center">
            {renderStepContent(step, index)}
          </div>

          {/* Connector Line */}
          {index < steps.length - 1 && (
            <div className="flex-1 flex items-center px-4 mt-5">
              <div className={`w-full ${getConnectorClasses(index)}`} />
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );

  const renderVerticalStepper = () => (
    <div className={`flex flex-col space-y-8 ${className}`}>
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-start">
          <div className="flex flex-col items-center mr-4">
            <div className={getStepClasses(getStepStatus(index))}>
              {getStepStatus(index) === 'completed' ? (
                <FaCheck className="text-sm" />
              ) : variant === 'numbered' ? (
                <span>{index + 1}</span>
              ) : step.icon ? (
                step.icon
              ) : (
                <FaCircle className="text-xs" />
              )}
            </div>
            
            {/* Vertical Connector */}
            {index < steps.length - 1 && (
              <div className={`w-0.5 h-16 mt-4 ${
                index < currentStep 
                  ? 'bg-gradient-to-b from-green-500 to-emerald-500' 
                  : 'bg-gray-300'
              }`} />
            )}
          </div>

          {/* Step Content */}
          <div className="flex-1 pb-8">
            <div className={`${sizeClasses.title} font-medium ${getTextClasses(getStepStatus(index))}`}>
              {step.title}
            </div>
            {step.description && (
              <div className={`${sizeClasses.text} text-gray-500 mt-1`}>
                {step.description}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  const renderMinimalStepper = () => (
    <div className={`flex items-center space-x-2 ${className}`}>
      {steps.map((step, index) => {
        const status = getStepStatus(index);
        return (
          <React.Fragment key={step.id}>
            <div className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-300 ${
              status === 'completed' 
                ? 'bg-green-100 text-green-700'
                : status === 'current'
                ? 'bg-blue-100 text-blue-700'
                : 'bg-gray-100 text-gray-500'
            }`}>
              {step.title}
            </div>
            {index < steps.length - 1 && (
              <div className="text-gray-400">→</div>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );

  if (variant === 'minimal') {
    return renderMinimalStepper();
  }

  return orientation === 'vertical' ? renderVerticalStepper() : renderHorizontalStepper();
};

export default Stepper;
