import React from 'react';
import { But<PERSON> } from '../ui';
import { FaProjectDiagram, FaPlus } from 'react-icons/fa';

interface EmptyStateProps {
  onAddProject: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({ onAddProject }) => {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <FaProjectDiagram className="text-4xl text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
      <p className="text-gray-500 mb-6">Get started by creating your first project.</p>
      <Button onClick={onAddProject} className="bg-purple-600 hover:bg-purple-700">
        <FaPlus className="mr-2" />
        Add Project
      </Button>
    </div>
  );
};

export default EmptyState;
