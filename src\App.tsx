import { Routes, Route } from 'react-router-dom';
import { Home, Projects, ProjectDetails, ApplicationProcess, Contact, TrackApplication } from './pages';
import { ScrollToTop } from './components/ui';
import { PrivateRoute, PublicRoute } from './components/auth';
import { AdminLayout } from './components/admin';
import { WebSocketProvider } from './contexts/WebSocketContext';
import { PublicWebSocketProvider } from './contexts/PublicWebSocketContext';
import AdminLogin from './pages/admin/AdminLogin';
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminProjects from './pages/admin/AdminProjects';
import AdminApplications from './pages/admin/AdminApplications';
import AdminUsers from './pages/admin/AdminUsers';
import AdminTeams from './pages/admin/AdminTeams';
import AdminContacts from './pages/admin/AdminContacts';
import Unauthorized from './pages/admin/Unauthorized';
import { useEffect } from 'react';

function App() {

  useEffect(() => {
    console.log('App rendered');
    console.log('Vite env:', import.meta.env.VITE_NODE_ENV);
    console.log('Vite API Base URL:', import.meta.env.VITE_API_BASE_URL);
  }, []);

  return (
    <>
      {/* Skip link for accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>
      <ScrollToTop />
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<Home />} />

        {/* Projects page with WebSocket for real-time updates */}
        <Route path="/projects" element={
          <PublicWebSocketProvider>
            <Projects />
          </PublicWebSocketProvider>
        } />

        <Route path="/project/:id" element={<ProjectDetails />} />
        <Route path="/apply/:id" element={<ApplicationProcess />} />
        <Route path="/contact" element={<Contact />} />

        {/* Tracking Routes */}
        <Route path="/track" element={<TrackApplication />} />
        <Route path="/track/contact-inquiry/:id" element={<TrackApplication />} />
        <Route path="/track/internship-application/:id" element={<TrackApplication />} />

        {/* Admin Public Routes */}
        <Route
          path="/admin/login"
          element={
            <PublicRoute>
              <AdminLogin />
            </PublicRoute>
          }
        />
        <Route path="/admin/unauthorized" element={<Unauthorized />} />

        {/* Admin Private Routes */}
        <Route
          path="/admin"
          element={
            <PrivateRoute requireAdmin={true}>
              <WebSocketProvider>
                <AdminLayout />
              </WebSocketProvider>
            </PrivateRoute>
          }
        >
          <Route index element={<AdminDashboard />} />
          <Route path="dashboard" element={<AdminDashboard />} />
          <Route path="projects" element={<AdminProjects />} />
          <Route path="applications" element={<AdminApplications />} />
          <Route path="users" element={<AdminUsers />} />
          <Route path="teams" element={<AdminTeams />} />
          <Route path="contacts" element={<AdminContacts />} />
          <Route path="settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Settings</h1><p className="text-gray-600 mt-2">Settings panel coming soon...</p></div>} />
        </Route>
      </Routes>
    </>
  );
}

export default App;
