import { useState } from 'react';

export interface ApplicationData {
  projectId: string;
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    university: string;
    course: string;
    year: string;
    cgpa: string;
  };
  experience: {
    programmingExperience: string;
    relevantProjects: string;
    skills: string[];
    githubProfile?: string;
    portfolioUrl?: string;
  };
  motivation: {
    whyInterested: string;
    careerGoals: string;
    availability: string;
  };
}

export const useApplication = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const submitApplication = async (): Promise<boolean> => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      setSubmitSuccess(false);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate random success/failure for demo
      const success = Math.random() > 0.1; // 90% success rate

      if (success) {
        setSubmitSuccess(true);
        return true;
      } else {
        throw new Error('Failed to submit application. Please try again.');
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'An unexpected error occurred');
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetApplicationState = () => {
    setSubmitError(null);
    setSubmitSuccess(false);
    setIsSubmitting(false);
  };

  return {
    submitApplication,
    isSubmitting,
    submitError,
    submitSuccess,
    resetApplicationState
  };
};
