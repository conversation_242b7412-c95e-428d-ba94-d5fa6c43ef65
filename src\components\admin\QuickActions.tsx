import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader } from '../ui';
import {
  FaUsers,
  FaProjectDiagram,
  FaClipboardList,
  FaEnvelope
} from 'react-icons/fa';

interface QuickAction {
  icon: React.ReactNode;
  title: string;
  description: string;
  bgColor: string;
  hoverColor: string;
  iconColor: string;
  path: string;
}

const QuickActions: React.FC = () => {
  const navigate = useNavigate();

  const actions: QuickAction[] = [
    {
      icon: <FaProjectDiagram />,
      title: 'Create New Project',
      description: 'Add a new internship project',
      bgColor: 'bg-blue-50',
      hoverColor: 'hover:bg-blue-100',
      iconColor: 'text-blue-600',
      path: '/admin/projects'
    },
    {
      icon: <FaClipboardList />,
      title: 'Review Applications',
      description: 'Process pending applications',
      bgColor: 'bg-green-50',
      hoverColor: 'hover:bg-green-100',
      iconColor: 'text-green-600',
      path: '/admin/applications'
    },
    {
      icon: <FaUsers />,
      title: 'Manage Users',
      description: 'View and manage user accounts',
      bgColor: 'bg-purple-50',
      hoverColor: 'hover:bg-purple-100',
      iconColor: 'text-purple-600',
      path: '/admin/users'
    },
    {
      icon: <FaEnvelope />,
      title: 'Contact Management',
      description: 'Handle customer inquiries and support',
      bgColor: 'bg-orange-50',
      hoverColor: 'hover:bg-orange-100',
      iconColor: 'text-orange-600',
      path: '/admin/contacts'
    }
  ];

  const handleActionClick = (path: string) => {
    navigate(path);
  };

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
        <p className="text-sm text-gray-600">Common administrative tasks</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={() => handleActionClick(action.path)}
              className={`w-full flex items-center space-x-3 p-4 ${action.bgColor} ${action.hoverColor} rounded-lg transition-colors text-left cursor-pointer`}
            >
              <div className={action.iconColor}>
                {action.icon}
              </div>
              <div>
                <p className="font-medium text-gray-900">{action.title}</p>
                <p className="text-sm text-gray-600">{action.description}</p>
              </div>
            </button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActions;
