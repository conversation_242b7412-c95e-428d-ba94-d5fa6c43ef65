import apiService from './api';

// Application related interfaces
export interface SendOTPRequest {
  email: string;
  projectId: string;
}

export interface SendOTPResponse {
  message: string;
  type: 'SUCCESS' | 'ERROR' | 'INFO';
  data: null;
  status: number;
}

export interface VerifyOTPRequest {
  email: string;
  otp: string;
}

export interface VerifyOTPResponse {
  message: string;
  type: 'INFO' | 'ERROR';
  data: null;
  status: number;
}

export interface InternAnswer {
  questionType: 'BOOLEAN' | 'SELECT' | 'MULTISELECT' | 'TEXT';
  question: string;
  answer: string;
  multiSelectAnswer: string[];
  required: boolean;
}

export interface CreateApplicationRequest {
  name: string;
  email: string;
  phone: string;
  projectId: number;
  internAnswers: InternAnswer[];
  skills: string[];
  linkdinLink: string;
  portfolioLink: string;
  githubLink: string;
  resume: File;
}

export interface CreateApplicationResponse {
  message: string;
  type: 'INFO' | 'ERROR';
  data: {
    applicationId: number;
  };
  status: number;
}

class ApplicationService {
  // Send OTP to email for project application
  async sendOTP(email: string, projectId: string): Promise<SendOTPResponse> {
    try {
      const response = await apiService.post<SendOTPResponse>('/intern/send-otp', null, {
        params: {
          email,
          projectId
        }
      });

      return response;
    } catch (error: any) {
      console.error('Error sending OTP:', error);

      // Handle specific error cases
      if (error.response?.status === 406) {
        throw new Error(error.response.data?.message || "You've already applied to this project");
      }

      throw new Error(error.response?.data?.message || 'Failed to send OTP');
    }
  }

  // Verify OTP
  async verifyOTP(email: string, otp: string): Promise<VerifyOTPResponse> {
    try {
      const response = await apiService.post<VerifyOTPResponse>('/intern/verify-otp', null, {
        params: {
          email,
          otp
        }
      });
      return response;
    } catch (error: any) {
      console.error('Error verifying OTP:', error);
      throw new Error(error.response?.data?.message || 'Failed to verify OTP');
    }
  }

  // Create application with form data and file upload
  async createApplication(applicationData: CreateApplicationRequest): Promise<CreateApplicationResponse> {
    try {
      // Create FormData for file upload
      const formData = new FormData();

      // Add resume file
      formData.append('resume', applicationData.resume);

      // Add all other data as a single JSON string under 'data' key
      const applicationPayload = {
        name: applicationData.name,
        email: applicationData.email,
        phone: applicationData.phone,
        projectId: applicationData.projectId,
        internAnswers: applicationData.internAnswers,
        skills: applicationData.skills,
        linkdinLink: applicationData.linkdinLink,
        portfolioLink: applicationData.portfolioLink,
        githubLink: applicationData.githubLink
      };

      // Add JSON data as a single 'data' part
      formData.append('data', JSON.stringify(applicationPayload));

      const response = await apiService.postFormData<CreateApplicationResponse>('/intern/add-applicant', formData);
      
      return response;
    } catch (error: any) {
      console.error('Error creating application:', error);
      console.error('Error response data:', error.response?.data);
      console.error('Error status:', error.response?.status);

      // Handle specific error cases
      if (error.response?.status === 406) {
        throw new Error(error.response.data?.message || "You've already applied to this project");
      }

      // For 400 errors, try to get more specific error message
      if (error.response?.status === 400) {
        const errorMessage = error.response.data?.message || error.response.data?.error || 'Bad request - please check your data';
        throw new Error(errorMessage);
      }

      throw new Error(error.response?.data?.message || 'Failed to create application');
    }
  }

  // Helper method to format prerequisite answers for API
  formatPrerequisiteAnswers(answers: Record<string, any>, questions: any[]): InternAnswer[] {
    console.log('Formatting answers:', answers, 'for questions:', questions);
    return questions.map((question, index) => {
      const questionId = question.id || `question-${index}`;
      const answer = answers[questionId];
      
      return {
        questionType: question.questionType,
        question: question.text,
        answer: question.questionType === 'MULTISELECT' ? '' : String(answer || ''),
        multiSelectAnswer: question.questionType === 'MULTISELECT' ? (Array.isArray(answer) ? answer : []) : [],
        required: question.required
      };
    });
  }
}

// Create and export singleton instance
const applicationService = new ApplicationService();
export default applicationService;
