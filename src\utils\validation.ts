// Validation utilities for form inputs

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

export const validateRequired = (value: string): boolean => {
  return value.trim().length > 0;
};

export const validateMinLength = (value: string, minLength: number): boolean => {
  return value.trim().length >= minLength;
};

export const validateMaxLength = (value: string, maxLength: number): boolean => {
  return value.trim().length <= maxLength;
};

export const validateYear = (year: string): boolean => {
  const currentYear = new Date().getFullYear();
  const yearNum = parseInt(year);
  return yearNum >= 2020 && yearNum <= currentYear + 10;
};

export const validateFileSize = (file: File, maxSizeMB: number): boolean => {
  return file.size <= maxSizeMB * 1024 * 1024;
};

export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  return allowedTypes.includes(`.${fileExtension}`);
};

export const validateArray = (arr: any[], minLength: number = 1): boolean => {
  return Array.isArray(arr) && arr.length >= minLength;
};

// Comprehensive form validation
export interface ValidationRule {
  field: string;
  value: any;
  rules: {
    required?: boolean;
    email?: boolean;
    phone?: boolean;
    minLength?: number;
    maxLength?: number;
    year?: boolean;
    array?: { minLength?: number };
    custom?: (value: any) => boolean;
  };
  message?: string;
}

export const validateForm = (rules: ValidationRule[]): Record<string, string> => {
  const errors: Record<string, string> = {};

  rules.forEach(({ field, value, rules: fieldRules, message }) => {
    if (fieldRules.required && !validateRequired(String(value || ''))) {
      errors[field] = message || `${field} is required`;
      return;
    }

    if (value && fieldRules.email && !validateEmail(value)) {
      errors[field] = message || 'Please enter a valid email address';
      return;
    }

    if (value && fieldRules.phone && !validatePhone(value)) {
      errors[field] = message || 'Please enter a valid phone number';
      return;
    }

    if (value && fieldRules.minLength && !validateMinLength(value, fieldRules.minLength)) {
      errors[field] = message || `Minimum ${fieldRules.minLength} characters required`;
      return;
    }

    if (value && fieldRules.maxLength && !validateMaxLength(value, fieldRules.maxLength)) {
      errors[field] = message || `Maximum ${fieldRules.maxLength} characters allowed`;
      return;
    }

    if (value && fieldRules.year && !validateYear(value)) {
      errors[field] = message || 'Please enter a valid year';
      return;
    }

    if (fieldRules.array && !validateArray(value, fieldRules.array.minLength)) {
      errors[field] = message || 'This field is required';
      return;
    }

    if (fieldRules.custom && !fieldRules.custom(value)) {
      errors[field] = message || 'Invalid value';
      return;
    }
  });

  return errors;
};
