import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, Input, Textarea, Select, Modal } from '../ui';
import { Contact } from '../../types/contact';
import contactService from '../../services/contactService';

interface ContactEditModalProps {
  contact: Contact | null;
  isOpen: boolean;
  onClose: () => void;
  onSaveContact: (contact: Contact) => void;
}

const ContactEditModal: React.FC<ContactEditModalProps> = ({
  contact,
  isOpen,
  onClose,
  onSaveContact
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    type: 'GENERAL' as Contact['type'],
    status: 'NEW' as Contact['status'],
    priority: 'MEDIUM' as Contact['priority'],
    source: 'WEBSITE' as Contact['source'],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (contact) {
      setFormData({
        name: contact.name,
        email: contact.email,
        phone: contact.phone,
        subject: contact.subject,
        message: contact.message,
        type: contact.type,
        status: contact.status,
        priority: contact.priority,
        source: contact.source,
      });
    } else {
      // Reset form for new contact
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
        type: 'GENERAL',
        status: 'NEW',
        priority: 'MEDIUM',
        source: 'WEBSITE',
      });
    }
    setErrors({});
  }, [contact, isOpen]);

  if (!isOpen) return null;

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };



  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    if (!formData.subject.trim()) newErrors.subject = 'Subject is required';
    if (!formData.message.trim()) newErrors.message = 'Message is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    const now = new Date().toISOString();
    const contactData: Contact = {
      id: contact?.id || 0,
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      subject: formData.subject,
      message: formData.message,
      type: formData.type,
      status: formData.status,
      priority: formData.priority,
      source: formData.source,
      note: contact?.note || [],
      createdAt: contact?.createdAt || now,
      lastUpdate: contact?.lastUpdate || now
    };

    onSaveContact(contactData);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={contact ? 'Edit Contact' : 'Add New Contact'}
      size="lg"
    >
      {/* Form */}
      <form onSubmit={handleSubmit} className="max-h-[60vh] overflow-y-auto">
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Name *"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    error={errors.name}
                    placeholder="Enter full name"
                  />
                  <Input
                    label="Email *"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    error={errors.email}
                    placeholder="Enter email address"
                  />
                </div>

                <Input
                  label="Phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="Enter phone number"
                />
              </CardContent>
            </Card>

            {/* Contact Details */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">Contact Details</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Select
                    label="Contact Type *"
                    value={formData.type}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    options={contactService.getContactTypes()}
                  />
                  <Input
                    label="Subject *"
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    error={errors.subject}
                    placeholder="Enter subject"
                  />
                </div>

                <Textarea
                  label="Message *"
                  value={formData.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  error={errors.message}
                  placeholder="Enter message content"
                  rows={4}
                />
              </CardContent>
            </Card>

            {/* Status & Assignment */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">Status & Assignment</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Select
                    label="Status"
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    options={contactService.getStatusOptions()}
                  />
                  <Select
                    label="Priority"
                    value={formData.priority}
                    onChange={(e) => handleInputChange('priority', e.target.value)}
                    options={contactService.getPriorityOptions()}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Select
                    label="Source"
                    value={formData.source}
                    onChange={(e) => handleInputChange('source', e.target.value)}
                    options={contactService.getSourceOptions()}
                  />
                </div>
              </CardContent>
            </Card>


          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
            <Button
              type="button"
              onClick={onClose}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="gradient"
            >
              {contact ? 'Update Contact' : 'Create Contact'}
            </Button>
          </div>
        </form>
    </Modal>
  );
};

export default ContactEditModal;
