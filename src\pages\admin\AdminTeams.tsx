import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '../../components/ui';
import { FaUsers, FaPlus, FaEdit, FaTrash, FaUserTie, FaUserGraduate, FaChartBar, FaProjectDiagram } from 'react-icons/fa';
import { useTeams } from '../../hooks/useTeams';
import { Team as ApiTeam } from '../../services/teamService';
import { TeamFormModal, TeamsFilter, LoadingState, ErrorState, EmptyState } from '../../components/admin-teams';
import { Team as ComponentTeam } from '../../components/admin-teams/TeamCard';
import { User as ComponentUser } from '../../components/admin-users/UserCard';

const AdminTeams: React.FC = () => {
  const {
    teams,
    users,
    projects,
    teamStatusCount,
    userCount,
    loading,
    error,
    filters,
    refreshTeams,
    createTeam,
    updateTeam,
    deleteTeam,
    updateFilters,
    applyFilters,
    resetFilters,
  } = useTeams();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<ApiTeam | null>(null);

  // Convert API team to component team format
  const convertApiTeamToComponentTeam = (apiTeam: ApiTeam): ComponentTeam => {
    // Convert API team users to component users and group by role
    const teamUsers = apiTeam.users || [];

    const managers = teamUsers
      .filter(user => user.role === 'MANAGER')
      .map(user => {
        const fullUser = users.find(u => u.name === user.name);
        return fullUser ? convertApiUserToComponentUser(fullUser) : null;
      })
      .filter(user => user !== null) as ComponentUser[];

    const teamLeaders = teamUsers
      .filter(user => user.role === 'TL')
      .map(user => {
        const fullUser = users.find(u => u.name === user.name);
        return fullUser ? convertApiUserToComponentUser(fullUser) : null;
      })
      .filter(user => user !== null) as ComponentUser[];

    const interns = teamUsers
      .filter(user => user.role === 'INTERN')
      .map(user => {
        const fullUser = users.find(u => u.name === user.name);
        return fullUser ? convertApiUserToComponentUser(fullUser) : null;
      })
      .filter(user => user !== null) as ComponentUser[];

    return {
      id: apiTeam.teamId.toString(),
      name: apiTeam.title || '',
      description: apiTeam.description || '',
      status: (apiTeam.teamStatus?.toLowerCase() || 'active') as 'active' | 'inactive',
      createdDate: new Date().toISOString(),
      managers: managers,
      teamLeaders: teamLeaders,
      interns: interns,
      projects: apiTeam.projects?.map(p => p.projectName) || []
    };
  };

  // Convert single API user to component user format
  const convertApiUserToComponentUser = (user: any): ComponentUser => {
    return {
      id: user.id || 0,
      name: user.name || 'Unknown',
      email: '',
      role: user.role || 'INTERN',
      status: 'ACTIVE' as const,
      phone: null,
      domain: null,
      skills: null,
      githubLink: null,
      portfolioLink: null,
      linkedinLink: null
    };
  };

  // Convert API users to component users format
  const convertApiUsersToComponentUsers = (apiUsers: any[]): ComponentUser[] => {
    if (!apiUsers || !Array.isArray(apiUsers)) {
      return [];
    }

    return apiUsers.map(user => convertApiUserToComponentUser(user));
  };

  const handleAddTeam = () => {
    setSelectedTeam(null);
    setIsModalOpen(true);
  };

  const handleEditTeam = (team: ApiTeam) => {
    setSelectedTeam(team);
    setIsModalOpen(true);
  };

  const handleDeleteTeam = async (teamId: number) => {
    if (window.confirm('Are you sure you want to delete this team?')) {
      try {
        await deleteTeam(teamId);
      } catch (error) {
        console.error('Failed to delete team:', error);
        alert('Failed to delete team. Please try again.');
      }
    }
  };

  const handleFormSubmit = async (formData: any) => {
    try {
      // Extract user IDs from selected users
      const managerIds = formData.managers?.map((m: any) => m.id) || [];
      const teamLeaderIds = formData.teamLeaders?.map((tl: any) => tl.id) || [];
      const internIds = formData.interns?.map((i: any) => i.id) || [];
      const allUserIds = [...managerIds, ...teamLeaderIds, ...internIds];

      // Extract project IDs from selected project names
      const projectIds = (formData.projects || []).map((projectName: string) => {
        const project = projects.find(p => p.projectName === projectName);
        return project ? project.projectId : null;
      }).filter((id: number | null) => id !== null);

      const teamData = {
        teamName: formData.name,
        teamDescription: formData.description,
        teamStatus: formData.status.toUpperCase() as 'ACTIVE' | 'INACTIVE',
        userIds: allUserIds,
        projectIds: projectIds
      };

      if (selectedTeam) {
        await updateTeam(selectedTeam.teamId, teamData);
      } else {
        await createTeam(teamData);
      }

      setIsModalOpen(false);
      setSelectedTeam(null);
    } catch (error) {
      console.error('Failed to save team:', error);
      alert('Failed to save team. Please try again.');
    }
  };

  if (loading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={refreshTeams} />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Team Management</h1>
          <p className="text-gray-600 mt-1">Manage your teams and their members</p>
        </div>
        <Button onClick={handleAddTeam} className="bg-blue-600 hover:bg-blue-700">
          <FaPlus className="mr-2" />
          Add Team
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FaUsers className="text-3xl text-blue-600 mr-4" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Teams</p>
                <p className="text-2xl font-bold text-gray-900">{teams.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FaChartBar className="text-3xl text-green-600 mr-4" />
              <div>
                <p className="text-sm font-medium text-gray-600">Active Teams</p>
                <p className="text-2xl font-bold text-gray-900">{teamStatusCount.activeCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FaUserTie className="text-3xl text-purple-600 mr-4" />
              <div>
                <p className="text-sm font-medium text-gray-600">Managers</p>
                <p className="text-2xl font-bold text-gray-900">{userCount.managerCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FaUserGraduate className="text-3xl text-orange-600 mr-4" />
              <div>
                <p className="text-sm font-medium text-gray-600">Interns</p>
                <p className="text-2xl font-bold text-gray-900">{userCount.internCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filter Component */}
      <TeamsFilter
        filters={filters}
        projects={projects}
        onFiltersChange={updateFilters}
        onApplyFilters={applyFilters}
        onResetFilters={resetFilters}
        loading={loading}
      />

      {/* Teams Grid */}
      {teams.length === 0 ? (
        <EmptyState onAddTeam={handleAddTeam} />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teams.map((team) => {
          // Convert API team to component team for consistent data format
          const componentTeam = convertApiTeamToComponentTeam(team);

          return (
            <Card key={team.teamId} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{componentTeam.name}</h3>
                    <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${
                      componentTeam.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {componentTeam.status}
                    </span>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      onClick={() => handleEditTeam(team)}
                      className="bg-blue-500 hover:bg-blue-600"
                    >
                      <FaEdit />
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleDeleteTeam(team.teamId)}
                      className="bg-red-500 hover:bg-red-600"
                    >
                      <FaTrash />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4">{componentTeam.description}</p>

                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <FaUsers className="mr-2 text-gray-400" />
                    <span>{(componentTeam.managers.length + componentTeam.teamLeaders.length + componentTeam.interns.length)} members</span>
                  </div>

                  <div className="flex items-center text-sm">
                    <FaProjectDiagram className="mr-2 text-gray-400" />
                    <span>{componentTeam.projects.length} projects</span>
                  </div>
                </div>

                {(componentTeam.managers.length + componentTeam.teamLeaders.length + componentTeam.interns.length) > 0 && (
                  <div className="mt-4">
                    <p className="text-xs font-medium text-gray-600 mb-2">Team Members:</p>
                    <div className="flex flex-wrap gap-1">
                      {/* Show managers */}
                      {componentTeam.managers.slice(0, 2).map((user, index) => (
                        <span key={`manager-${index}`} className="px-2 py-1 bg-purple-100 text-xs rounded">
                          {user.name} (Manager)
                        </span>
                      ))}
                      {/* Show team leaders */}
                      {componentTeam.teamLeaders.slice(0, 2).map((user, index) => (
                        <span key={`tl-${index}`} className="px-2 py-1 bg-blue-100 text-xs rounded">
                          {user.name} (TL)
                        </span>
                      ))}
                      {/* Show interns */}
                      {componentTeam.interns.slice(0, 2).map((user, index) => (
                        <span key={`intern-${index}`} className="px-2 py-1 bg-green-100 text-xs rounded">
                          {user.name} (Intern)
                        </span>
                      ))}
                      {(componentTeam.managers.length + componentTeam.teamLeaders.length + componentTeam.interns.length) > 6 && (
                        <span className="px-2 py-1 bg-gray-100 text-xs rounded">
                          +{(componentTeam.managers.length + componentTeam.teamLeaders.length + componentTeam.interns.length) - 6} more
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
        </div>
      )}

      {/* Add/Edit Team Modal */}
      <TeamFormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleFormSubmit}
        selectedTeam={selectedTeam ? convertApiTeamToComponentTeam(selectedTeam) : null}
        users={convertApiUsersToComponentUsers(users)}
        projects={projects}
      />
    </div>
  );
};

export default AdminTeams;
