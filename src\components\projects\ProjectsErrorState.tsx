import React from 'react';
import { <PERSON><PERSON>, Footer } from '../layout';
import { Button } from '../ui';
import { FaExclamationTriangle } from 'react-icons/fa';

interface ProjectsErrorStateProps {
  error: string;
}

const ProjectsErrorState: React.FC<ProjectsErrorStateProps> = ({ error }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50 flex flex-col">
      <Header />
      <div className="flex-1 flex items-center justify-center px-4 py-20">
        <div className="text-center w-full max-w-lg">
          <div className="flex justify-center mb-8">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
              <FaExclamationTriangle className="text-red-500 text-3xl" />
            </div>
          </div>
          <div className="space-y-6">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900">Something went wrong</h3>
            <p className="text-lg text-red-600 leading-relaxed max-w-md mx-auto">{error}</p>
            <div className="pt-4">
              <Button onClick={() => window.location.reload()} variant="gradient" size="lg">
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ProjectsErrorState;
