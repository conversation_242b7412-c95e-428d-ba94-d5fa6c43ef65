import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../ui';
import {
  FaCode,
  FaArrowRight,
  FaGraduationCap,
  FaEye,
  FaCog,
  FaDatabase,
  FaRobot,
  FaChartLine,
  FaShieldAlt,
  FaFlask,
  FaIndustry,
  FaComments,
  FaCloud,
  FaVial,
  FaBullseye
} from 'react-icons/fa';

// Import project images
import relayBoxImage from '../../assets/projects/relay-box.jpg';
import gentanAiImage from '../../assets/projects/gentan-ai.png';
import laserPickingImage from '../../assets/projects/laser-picking.png';
import ehsChatImage from '../../assets/projects/ehs-chat.png';
import igaiAiImage from '../../assets/projects/azure-aws-agent.png';
import smartApiTestImage from '../../assets/projects/smart-api-test.png';

interface SlideContent {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  features: { text: string; icon: React.ReactNode }[];
  cta: string;
  ctaIcon: React.ReactNode;
  stats: { value: string; label: string }[];
  mainIcon: React.ReactNode;
}

const HeroSection: React.FC = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [slideProgress, setSlideProgress] = useState(0);

  const slides: SlideContent[] = [
    {
      id: 1,
      title: "AI-Powered Industrial Relay Detection",
      subtitle: "Advanced Computer Vision for Industrial Automation",
      description: "Revolutionary AI system using YOLO models to detect relay components and circuits with precision. Built with C#, Python, EmguCV, SQL Server, and Flask for seamless industrial integration.",
      image: relayBoxImage,
      features: [
        { text: "YOLOv5 & YOLOAct Models", icon: <FaRobot className="text-cyan-400" /> },
        { text: "Real-time Detection", icon: <FaEye className="text-green-400" /> },
        { text: "Industrial Grade", icon: <FaIndustry className="text-purple-400" /> },
        { text: "Multi-tech Stack", icon: <FaCog className="text-pink-400" /> }
      ],
      cta: "View AI Projects",
      ctaIcon: <FaRobot />,
      mainIcon: <FaRobot />,
      stats: [
        { value: "99.5%", label: "Detection Accuracy" },
        { value: "Real-time", label: "Processing Speed" },
        { value: "Industrial", label: "Grade Solution" }
      ]
    },
    {
      id: 2,
      title: "AI-Based Fabric Width Detection",
      subtitle: "Precision Fabric Width Detection System",
      description: "Cutting-edge AI solution using Segment Anything Model (SAM) for accurate cloth width detection. Developed with C#, Python, OpenCV, SQL Server, and Flask for textile industry automation.",
      image: gentanAiImage,
      features: [
        { text: "SAM Model Integration", icon: <FaRobot className="text-cyan-400" /> },
        { text: "Precision Measurement", icon: <FaChartLine className="text-green-400" /> },
        { text: "OpenCV Processing", icon: <FaEye className="text-purple-400" /> },
        { text: "Textile Industry", icon: <FaIndustry className="text-pink-400" /> }
      ],
      cta: "Explore AI Solutions",
      ctaIcon: <FaRobot />,
      mainIcon: <FaChartLine />,
      stats: [
        { value: "±0.1mm", label: "Precision Accuracy" },
        { value: "SAM", label: "AI Model" },
        { value: "Textile", label: "Industry Focus" }
      ]
    },
    {
      id: 3,
      title: "Warehouse Laser Picking Guidance",
      subtitle: "Patented Warehouse Automation Technology",
      description: "Revolutionary warehouse picking system using C++, OpenCV, and SQL Server. QR code selection triggers laser pointing to guide workers to correct boxes. Patent-protected innovation for logistics optimization.",
      image: laserPickingImage,
      features: [
        { text: "Patent Protected", icon: <FaShieldAlt className="text-cyan-400" /> },
        { text: "QR Code Integration", icon: <FaCode className="text-green-400" /> },
        { text: "Laser Guidance", icon: <FaBullseye className="text-purple-400" /> },
        { text: "Warehouse Automation", icon: <FaIndustry className="text-pink-400" /> }
      ],
      cta: "View Innovation",
      ctaIcon: <FaBullseye />,
      mainIcon: <FaBullseye />,
      stats: [
        { value: "Patent", label: "Protected IP" },
        { value: "C++", label: "Core Technology" },
        { value: "Warehouse", label: "Automation" }
      ]
    },
    {
      id: 4,
      title: "AI Chat Assistant for Educational Institutions",
      subtitle: "Intelligent School Information Assistant",
      description: "Advanced conversational AI system built with Python, Llama LLM, Vector Database, and React. Provides comprehensive school information and support through intelligent chat interactions.",
      image: ehsChatImage,
      features: [
        { text: "Llama LLM Integration", icon: <FaRobot className="text-cyan-400" /> },
        { text: "Vector Database", icon: <FaDatabase className="text-green-400" /> },
        { text: "Conversational AI", icon: <FaComments className="text-purple-400" /> },
        { text: "Educational Focus", icon: <FaGraduationCap className="text-pink-400" /> }
      ],
      cta: "Try AI Chat",
      ctaIcon: <FaComments />,
      mainIcon: <FaComments />,
      stats: [
        { value: "Llama", label: "LLM Model" },
        { value: "Vector", label: "Database" },
        { value: "Education", label: "Domain" }
      ]
    },
    {
      id: 5,
      title: "Cloud IAM & Security Monitoring AI",
      subtitle: "Cloud IAM Management & Log Tracking",
      description: "Comprehensive cloud security solution using Spring Boot, Flask, OpenAI LLM, React, and Neo4j Graph Database. Advanced AWS and Azure IAM management with intelligent log tracking capabilities.",
      image: igaiAiImage,
      features: [
        { text: "Multi-Cloud Support", icon: <FaCloud className="text-cyan-400" /> },
        { text: "IAM Management", icon: <FaShieldAlt className="text-green-400" /> },
        { text: "Graph Database", icon: <FaDatabase className="text-purple-400" /> },
        { text: "Log Tracking", icon: <FaChartLine className="text-pink-400" /> }
      ],
      cta: "Explore Cloud AI",
      ctaIcon: <FaCloud />,
      mainIcon: <FaShieldAlt />,
      stats: [
        { value: "AWS+Azure", label: "Cloud Platforms" },
        { value: "Neo4j", label: "Graph DB" },
        { value: "Security", label: "Focus" }
      ]
    },
    {
      id: 6,
      title: "AI-Driven API Testing Platform",
      subtitle: "AI-Powered API Testing Platform",
      description: "Intelligent API testing solution leveraging Flask, OpenAI LLM, and React. Automated collection-based API testing using artificial intelligence for comprehensive test coverage and validation.",
      image: smartApiTestImage,
      features: [
        { text: "AI-Powered Testing", icon: <FaRobot className="text-cyan-400" /> },
        { text: "OpenAI Integration", icon: <FaFlask className="text-green-400" /> },
        { text: "Collection-based", icon: <FaVial className="text-purple-400" /> },
        { text: "Automated Testing", icon: <FaCog className="text-pink-400" /> }
      ],
      cta: "Test with AI",
      ctaIcon: <FaVial />,
      mainIcon: <FaFlask />,
      stats: [
        { value: "OpenAI", label: "LLM Model" },
        { value: "Flask", label: "Backend" },
        { value: "API", label: "Testing Focus" }
      ]
    }
  ];

  useEffect(() => {
    // Reset progress when slide changes
    setSlideProgress(0);

    // Progress animation - updates every 100ms for smooth animation
    const progressTimer = setInterval(() => {
      setSlideProgress((prev) => {
        const increment = 100 / 15000; // 100ms / 15000ms = progress per update
        const newProgress = prev + increment;
        return newProgress >= 1 ? 1 : newProgress;
      });
    }, 100);

    // Slide change timer - changes slide every 15 seconds
    const slideTimer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 30000);

    return () => {
      clearInterval(progressTimer);
      clearInterval(slideTimer);
    };
  }, [slides.length, currentSlide]);

  const handleSlideChange = (index: number) => {
    setCurrentSlide(index);
    setSlideProgress(0); // Reset progress when manually changing slides
  };

  const handleCTAClick = () => {
    navigate('/projects');
  };

  return (
    <section
      className="relative min-h-screen overflow-hidden pt-16 sm:pt-20 pb-20 sm:pb-24"
      aria-label="Hero section - MSS Internship industry project showcase"
      role="banner"
    >
      {/* Full-Screen Background Images */}
      <div className="absolute inset-0">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={slide.image}
              alt={slide.title}
              className="w-full h-full object-cover"
              loading={index === 0 ? 'eager' : 'lazy'}
            />
            {/* Dark overlay for better text readability */}
            <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-black/40"></div>
            {/* Additional gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-black/30"></div>
          </div>
        ))}
      </div>

      {/* Animated Accent Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-cyan-400/20 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-40 h-40 bg-purple-400/20 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-20 left-1/3 w-36 h-36 bg-pink-400/20 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Content Overlay */}
      <div className="relative z-10 min-h-screen flex items-center pt-8 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center min-h-[75vh]">
            {/* Main Content - Takes up more space */}
            <div className="lg:col-span-8 text-center lg:text-left">
              {/* Project Badge */}
              <div className="mb-6 sm:mb-8">

                {/* Project Title */}
                <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 text-transparent bg-clip-text leading-tight mb-6">
                  {slides[currentSlide].title}
                </h1>

                {/* Project Description */}
                <p className="text-lg sm:text-xl md:text-2xl text-gray-200 leading-relaxed max-w-4xl">
                  {slides[currentSlide].description}
                </p>
              </div>

              {/* Technology Features */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                {slides[currentSlide].features.map((feature, index) => (
                  <div
                    key={index}
                    className="group bg-white/5 backdrop-blur-md rounded-2xl p-4 border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300 hover:scale-105"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="text-2xl mb-3 group-hover:scale-110 transition-transform duration-300">
                      {feature.icon}
                    </div>
                    <p className="text-white text-sm font-medium leading-tight">{feature.text}</p>
                  </div>
                ))}
              </div>

              {/* Action Section */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6 mb-8">
                <Button
                  variant="gradient"
                  size="xl"
                  glow
                  className="group px-8 py-4 text-lg font-semibold"
                  onClick={handleCTAClick}
                >
                  <span className="flex items-center space-x-3">
                    <span className="text-xl">{slides[currentSlide].ctaIcon}</span>
                    <span>{slides[currentSlide].cta}</span>
                    <FaArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </span>
                </Button>

                {/* Project Stats */}
                <div className="flex items-center space-x-6">
                  {slides[currentSlide].stats.map((stat, index) => (
                    <div
                      key={index}
                      className="text-center"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <div className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 text-transparent bg-clip-text">
                        {stat.value}
                      </div>
                      <div className="text-gray-300 text-sm font-medium">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Project Info Sidebar */}
            <div className="lg:col-span-4">
              <div className="bg-white/5 backdrop-blur-md rounded-3xl p-6 border border-white/10">
                <h3 className="text-xl font-bold text-white mb-4">Project Details</h3>

                {/* Tech Stack */}
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-300 uppercase tracking-wide mb-3">Technologies Used</h4>
                  <div className="flex flex-wrap gap-2">
                    {slides[currentSlide].features.map((feature, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-white/10 rounded-full text-xs font-medium text-white border border-white/20"
                      >
                        {feature.text}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Project Metrics */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-300 uppercase tracking-wide mb-3">Key Metrics</h4>
                  <div className="space-y-3">
                    {slides[currentSlide].stats.map((stat, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-gray-300 text-sm">{stat.label}</span>
                        <span className="text-white font-semibold">{stat.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Slide Navigation */}
      <div className="absolute bottom-12 sm:bottom-16 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex items-center space-x-4">
          {slides.map((slide, index) => (
            <button
              key={index}
              onClick={() => handleSlideChange(index)}
              className={`group relative transition-all duration-300 ${
                index === currentSlide ? 'scale-110' : 'hover:scale-105'
              }`}
              aria-label={`Go to ${slide.title}`}
            >
              {/* Slide Indicator */}
              <div className={`w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                index === currentSlide
                  ? 'border-white bg-white/20 backdrop-blur-md'
                  : 'border-white/40 bg-white/5 hover:border-white/60 hover:bg-white/10'
              }`}>
                <div className="w-full h-full flex items-center justify-center text-white">
                  {slide.mainIcon}
                </div>
              </div>

              {/* Progress Ring */}
              {index === currentSlide && (
                <div className="absolute inset-0 rounded-full">
                  <svg className="w-full h-full transform -rotate-90" viewBox="0 0 48 48">
                    <circle
                      cx="24"
                      cy="24"
                      r="22"
                      fill="none"
                      stroke="rgba(255,255,255,0.2)"
                      strokeWidth="2"
                    />
                    <circle
                      cx="24"
                      cy="24"
                      r="22"
                      fill="none"
                      stroke="url(#gradient)"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeDasharray="138.23"
                      strokeDashoffset={138.23 * (1 - slideProgress)}
                      className="transition-all duration-300"
                    />
                    <defs>
                      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#06b6d4" />
                        <stop offset="50%" stopColor="#8b5cf6" />
                        <stop offset="100%" stopColor="#ec4899" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
              )}

              {/* Project Name Tooltip */}
              <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                <div className="bg-black/80 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-lg whitespace-nowrap">
                  {slide.title}
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
