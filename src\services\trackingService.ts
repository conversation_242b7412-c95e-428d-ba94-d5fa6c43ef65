import apiService from './api';

// Types for tracking responses
export interface ContactTrackingData {
  id: number;
  name: string;
  email: string;
  phone: string;
  type: 'GENERAL' | 'INTERNSHIP' | 'PARTNERSHIP' | 'TECHNICAL';
  note: Array<{
    id: number;
    note: string;
    createdAt: string;
    type: 'CALL' | 'MEETING' | 'EMAIL' | 'FOLLOW_UP' | 'NOTE';
  }>;
  subject: string;
  message: string;
  status: 'NEW' | 'INPROGRESS' | 'RESOLVED' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  source: 'WEBSITE' | 'REFERAL' | 'PHONE' | 'SOCIAL';
  createdAt: string;
  lastUpdate: string;
}

export interface InternshipTrackingData {
  name: string;
  email: string;
  phone: string;
  skills: string[];
  applicationStatus: 'PENDING' | 'REVIEWING' | 'ACCEPTED' | 'REJECTED';
  gitProfile: string;
  portolioLink: string;
  linkeinProfile: string;
  appliedAt: string;
  projectName: string;
}

export interface TrackingResponse<T> {
  message: string;
  type: 'INFO' | 'ERROR' | 'SUCCESS' | 'WARNING';
  data: T;
  status: number;
}

class TrackingService {
  // Track contact by ID
  async trackContact(contactId: string): Promise<TrackingResponse<ContactTrackingData>> {
    try {
      const data = await apiService.get<TrackingResponse<ContactTrackingData>>(`/contact/${contactId}`);
      return data;
    } catch (error) {
      console.error('Error tracking contact:', error);
      throw new Error('Failed to track contact. Please check your tracking ID and try again.');
    }
  }

  // Track internship application by ID
  async trackInternshipApplication(applicationId: string): Promise<TrackingResponse<InternshipTrackingData>> {
    try {
      const data = await apiService.get<TrackingResponse<InternshipTrackingData>>(`/intern/${applicationId}`);
      return data;
    } catch (error) {
      console.error('Error tracking internship application:', error);
      throw new Error('Failed to track application. Please check your tracking ID and try again.');
    }
  }



  // Get status color based on status
  getStatusColor(status: string): string {
    switch (status.toUpperCase()) {
      case 'NEW':
      case 'PENDING':
        return 'text-blue-600 bg-blue-100';
      case 'INPROGRESS':
      case 'REVIEWING':
        return 'text-yellow-600 bg-yellow-100';
      case 'RESOLVED':
      case 'ACCEPTED':
        return 'text-green-600 bg-green-100';
      case 'CLOSED':
      case 'REJECTED':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  }

  // Get priority color
  getPriorityColor(priority: string): string {
    switch (priority.toUpperCase()) {
      case 'LOW':
        return 'text-green-600 bg-green-100';
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100';
      case 'HIGH':
        return 'text-orange-600 bg-orange-100';
      case 'CRITICAL':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  }

  // Format date
  formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  }
}

export const trackingService = new TrackingService();
export default trackingService;
