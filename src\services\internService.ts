import apiService, { ApiResponse } from './api';

// Intern interfaces
export interface InternAnswer {
  questionType: 'BOOLEAN' | 'SELECT' | 'MULTISELECT' | 'TEXT';
  question: string;
  answer: string;
  multiSelectAnswer: string[];
  required: boolean;
}

export interface ApplicantNote {
  id: number;
  text: string;
  type: 'CALL' | 'MEETING' | 'EMAIL' | 'FOLLOW_UP' | 'NOTE' | 'GENERAL';
}

export interface AddApplicantNoteRequest {
  activityType: 'CALL' | 'MEETING' | 'EMAIL' | 'FOLLOW_UP' | 'NOTE';
  text: string;
}

export interface InternApplicant {
  id: number;
  name: string;
  applicationStatus: 'PENDING' | 'REVIEWING' | 'ACCEPTED' | 'REJECTED';
  projectName: string;
  appliedAt: string;
  answerCount: number;
  technologies: string[];
  phone: string;
  email: string;
  skills: string[] | null;
  internAnswers: InternAnswer[];
  applicantNotes: ApplicantNote[];
  status: string;
}

export interface ApplicationCount {
  rejected: number;
  reviewing: number;
  approved: number;
  pending: number;
  total: number;
}

export interface ProjectNameId {
  projectName: string;
  projectId: number;
}

export interface InternResponse {
  message: string;
  type: 'INFO' | 'ERROR';
  data: {
    applicationCount: ApplicationCount;
    applicantDetails: InternApplicant[];
    projectNameIds: ProjectNameId[];
  };
  status: number;
}



class InternService {
  // Get all intern applications
  async getInternApplications(filters?: {
    text?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    projectId?: string;
  }): Promise<InternResponse> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters?.text && filters.text.trim()) {
        queryParams.append('text', filters.text.trim());
      }

      if (filters?.startDate) {
        // Ensure date is in yyyy-mm-dd format
        const formattedStartDate = this.formatDateForAPI(filters.startDate);
        if (formattedStartDate) {
          queryParams.append('startDate', formattedStartDate);
        }
      }

      if (filters?.endDate) {
        // Ensure date is in yyyy-mm-dd format
        const formattedEndDate = this.formatDateForAPI(filters.endDate);
        if (formattedEndDate) {
          queryParams.append('endDate', formattedEndDate);
        }
      }

      if (filters?.status && filters.status !== 'ALL') {
        queryParams.append('status', filters.status);
      }

      if (filters?.projectId && filters.projectId !== 'ALL') {
        queryParams.append('projectId', filters.projectId);
      }

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/admin/intern?${queryString}` : '/admin/intern';

      const response = await apiService.get<InternResponse>(endpoint);
      return response;
    } catch (error: any) {
      console.error('Error fetching intern applications:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch intern applications');
    }
  }

  // Utility method to format date to yyyy-mm-dd
  private formatDateForAPI(dateString: string): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  // Change application status
  async changeApplicationStatus(id: number, status: InternApplicant['applicationStatus']): Promise<ApiResponse> {
    try {
      const response = await apiService.post<ApiResponse>(
        `/admin/intern/change/application/status/${id}`,
        null,
        {
          params: {
            applicationStatus: status
          }
        }
      );
      return response;
    } catch (error: any) {
      console.error('Error changing application status:', error);
      throw new Error(error.response?.data?.message || 'Failed to change application status');
    }
  }

  // Add applicant note
  async addApplicantNote(id: number, notes: AddApplicantNoteRequest[]): Promise<ApiResponse> {
    try {
      const response = await apiService.post<ApiResponse>(`/admin/add/applicant/note/${id}`, notes);
      return response;
    } catch (error: any) {
      console.error('Error adding applicant note:', error);
      throw new Error(error.response?.data?.message || 'Failed to add applicant note');
    }
  }

  // Delete applicant note
  async deleteApplicantNote(noteId: number, applicantId: number): Promise<ApiResponse> {
    try {
      const response = await apiService.delete<ApiResponse>(`/admin/intern/delete/note/${noteId}`, {
        params: {
          applicantId
        }
      });
      return response;
    } catch (error: any) {
      console.error('Error deleting applicant note:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete applicant note');
    }
  }

  // Delete application
  async deleteApplication(id: number): Promise<ApiResponse> {
    try {
      const response = await apiService.delete<ApiResponse>(`/admin/intern/delete/${id}`);
      return response;
    } catch (error: any) {
      console.error('Error deleting application:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete application');
    }
  }

  // Add application to user (add as intern)
  async addApplicationAsUser(id: number): Promise<ApiResponse> {
    try {
      const response = await apiService.post<ApiResponse>(`/admin/intern/add/user/${id}`);
      return response;
    } catch (error: any) {
      console.error('Error adding application as user:', error);
      throw new Error(error.response?.data?.message || 'Failed to add application as user');
    }
  }

  // Download resume with authentication
  async downloadResume(id: number,name: string): Promise<void> {
    try {
      // Use the axios instance directly for blob download
      const axiosInstance = apiService.getApiInstance();
      const response = await axiosInstance.get(`/admin/intern/resume/${id}`, {
        responseType: 'blob'
      });

      // Create blob URL and trigger download
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `resume_${name}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Error downloading resume:', error);
      throw new Error(error.response?.data?.message || 'Failed to download resume');
    }
  }

  // Get resume download URL (deprecated - use downloadResume instead)
  getResumeDownloadUrl(id: number): string {
    return `${apiService.getBaseUrl()}/admin/intern/resume/${id}`;
  }

  // Get application status display name
  getApplicationStatusDisplayName(status: InternApplicant['applicationStatus']): string {
    switch (status) {
      case 'PENDING':
        return 'Pending';
      case 'REVIEWING':
        return 'Reviewing';
      case 'ACCEPTED':
        return 'Accepted';
      case 'REJECTED':
        return 'Rejected';
      default:
        return status;
    }
  }

  // Get application status color
  getApplicationStatusColor(status: InternApplicant['applicationStatus']): string {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'REVIEWING':
        return 'bg-blue-100 text-blue-800';
      case 'ACCEPTED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Get note type display name
  getNoteTypeDisplayName(type: ApplicantNote['type']): string {
    switch (type) {
      case 'CALL':
        return 'Phone Call';
      case 'MEETING':
        return 'Meeting';
      case 'EMAIL':
        return 'Email';
      case 'FOLLOW_UP':
        return 'Follow Up';
      case 'NOTE':
        return 'Note';
      case 'GENERAL':
        return 'General';
      default:
        return type;
    }
  }

  // Get note type options for dropdowns
  getNoteTypeOptions() {
    return [
      { value: 'CALL', label: 'Phone Call' },
      { value: 'MEETING', label: 'Meeting' },
      { value: 'EMAIL', label: 'Email' },
      { value: 'FOLLOW_UP', label: 'Follow Up' },
      { value: 'NOTE', label: 'Note' }
    ];
  }

  // Get application status options for dropdowns
  getStatusOptions() {
    return [
      { value: 'PENDING', label: 'Pending' },
      { value: 'REVIEWING', label: 'Reviewing' },
      { value: 'ACTIVE', label: 'Active' },
      { value: 'REJECTED', label: 'Rejected' }
    ];
  }

  // Format date for display
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  }
}

const internService = new InternService();
export default internService;
