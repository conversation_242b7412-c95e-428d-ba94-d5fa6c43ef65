import React from 'react';
import { <PERSON><PERSON> } from '../ui';
import { FaUser, FaPlus } from 'react-icons/fa';

interface EmptyStateProps {
  onAddUser: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({ onAddUser }) => {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <FaUser className="text-4xl text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
      <p className="text-gray-500 mb-6">Get started by adding your first user.</p>
      <Button onClick={onAddUser} className="bg-green-600 hover:bg-green-700">
        <FaPlus className="mr-2" />
        Add User
      </Button>
    </div>
  );
};

export default EmptyState;
