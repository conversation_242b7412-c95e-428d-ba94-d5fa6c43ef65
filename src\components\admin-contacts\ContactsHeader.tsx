import React from 'react';
import { But<PERSON> } from '../ui';
import { FaPlus } from 'react-icons/fa';

interface ContactsHeaderProps {
  onAddContact: () => void;
}

const ContactsHeader: React.FC<ContactsHeaderProps> = ({ onAddContact }) => {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Contact Management</h1>
        <p className="text-gray-600 mt-1">Manage all customer inquiries, support requests, and partnership opportunities</p>
      </div>
      <Button
        onClick={onAddContact}
        variant="gradient"
        size="lg"
        className="flex items-center space-x-2"
        glow
      >
        <FaPlus />
        <span>Add Contact</span>
      </Button>
    </div>
  );
};

export default ContactsHeader;
