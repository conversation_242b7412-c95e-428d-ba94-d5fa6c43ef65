import React from 'react';
import { Card, CardContent, CardHeader } from '../ui';
import {
  FaCheckCircle,
  FaClock,
  FaExclamationTriangle,
  FaEye
} from 'react-icons/fa';
import dashboardService, { LatestApplicant } from '../../services/dashboardService';

interface RecentApplicationsProps {
  applications: LatestApplicant[];
  recentActivity: {
    totalApplications: number;
    pendingCount: number;
    reviewingCount: number;
    acceptedCount: number;
    rejectedCount: number;
    statusBreakdown: Record<string, number>;
  };
}

const RecentApplications: React.FC<RecentApplicationsProps> = ({
  applications,
  recentActivity
}) => {
  const getStatusColor = (status: LatestApplicant['applicationStatus']) => {
    return dashboardService.getApplicationStatusColor(status);
  };

  const getStatusIcon = (status: LatestApplicant['applicationStatus']) => {
    switch (status) {
      case 'ACCEPTED':
        return <FaCheckCircle className="text-green-500" />;
      case 'PENDING':
        return <FaClock className="text-yellow-500" />;
      case 'REVIEWING':
        return <FaEye className="text-blue-500" />;
      case 'REJECTED':
        return <FaExclamationTriangle className="text-red-500" />;
      default:
        return <FaClock className="text-gray-500" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold text-gray-900">Recent Applications</h3>
        <p className="text-sm text-gray-600">
          {recentActivity.totalApplications} applications • {recentActivity.pendingCount} pending
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {applications.length > 0 ? (
            applications.map((application, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                    {application.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{application.name}</p>
                    <p className="text-sm text-gray-600">{application.projectName}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.applicationStatus)}`}>
                    {dashboardService.getApplicationStatusDisplayName(application.applicationStatus)}
                  </span>
                  {getStatusIcon(application.applicationStatus)}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No recent applications</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentApplications;
