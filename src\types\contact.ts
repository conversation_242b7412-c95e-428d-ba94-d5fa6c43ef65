// Contact management types for admin panel

export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  inquiryType: string;
}

export interface Contact {
  id: number;
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  type: 'GENERAL' | 'INTERNSHIP' | 'PARTNERSHIP' | 'TECHNICAL';
  status: 'NEW' | 'INPROGRESS' | 'RESOLVED' | 'CLOSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  source: 'WEBSITE' | 'REFERAL' | 'PHONE' | 'SOCIAL';
  note: ContactNote[];
  createdAt: string;
  lastUpdate: string;
}

export interface ContactNote {
  id: number;
  note: string;
  createdAt: string;
  type: 'CALL' | 'MEETING' | 'EMAIL' | 'FOLLOW_UP' | 'NOTE';
}

export interface ContactStats {
  totalCount: number;
  newCount: number;
  inProgressCount: number;
  resolvingCount: number;
  closedCount: number;
}

export interface ContactFilter {
  status?: string;
  priority?: string;
  source?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  tags?: string[];
  search?: string;
}
