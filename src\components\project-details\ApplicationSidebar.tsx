import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, CardHeader, CardContent, Button } from '../ui';
import { 
  Fa<PERSON><PERSON>board<PERSON>ist, 
  FaUser<PERSON>heck, 
  Fa<PERSON>sers, 
  FaRocket,
} from 'react-icons/fa';
import { Project } from '../../hooks';

interface ApplicationSidebarProps {
  project: Project;
}

const ApplicationSidebar: React.FC<ApplicationSidebarProps> = ({ project }) => {
  const navigate = useNavigate();

  const handleApply = () => {
    navigate(`/apply/${project.id}`);
  };

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Application Stats */}
      <Card gradient>
        <CardHeader>
          <h3 className="text-lg lg:text-xl font-bold text-gray-900 flex items-center">
            <FaClipboardList className="mr-2 text-gray-600" />
            Application Status
          </h3>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <FaUserCheck className="text-green-600 text-sm" />
                <span className="text-gray-700 text-sm lg:text-base">Applications received:</span>
              </div>
              <span className="font-bold text-purple-600 text-sm lg:text-base">
                {project.applicationCount}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <FaUsers className="text-blue-600 text-sm" />
                <span className="text-gray-700 text-sm lg:text-base">Openings available:</span>
              </div>
              <span className="font-bold text-purple-600 text-sm lg:text-base">
                {project.maxInterns}
              </span>
            </div>
            <div className="text-center pt-3 lg:pt-4">
              
              <Button
                variant={ 'gradient'}
                size="lg"
                className="w-full font-bold text-sm lg:text-base flex items-center justify-center space-x-2"
                onClick={handleApply}
                glow
              >
                    <FaRocket />
                    <span>Apply Now</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApplicationSidebar;
