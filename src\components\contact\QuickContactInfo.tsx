import React from 'react';
import {
  FaEnvelope,
  FaPhone,
  FaClock,
  FaBolt,
  FaFacebookF,
  FaTwitter,
  FaLinkedinIn,
  FaInstagram,
  FaYoutube
} from 'react-icons/fa';

const QuickContactInfo: React.FC = () => {
  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-3xl font-bold text-gray-900 mb-6">
          Get in Touch <span className="gradient-text-primary">Today</span>
        </h2>
        <p className="text-lg text-gray-700 mb-8">
          Ready to start your tech journey? We're here to guide you every step of the way.
        </p>
      </div>

      {/* Quick Contact Cards */}
      <div className="space-y-6">
        <div className="flex items-start space-x-4 p-6 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-xl flex items-center justify-center flex-shrink-0">
            <FaEnvelope className="text-white text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-gray-900 mb-1">Email Us</h3>
            <p className="text-gray-600 text-sm mb-2">Send us a detailed message</p>
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800 transition-colors font-medium">
              <EMAIL>
            </a>
          </div>
        </div>

        <div className="flex items-start space-x-4 p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
          <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-xl flex items-center justify-center flex-shrink-0">
            <FaPhone className="text-white text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-gray-900 mb-1">Call Us</h3>
            <p className="text-gray-600 text-sm mb-2">Speak directly with our team</p>
            <a href="tel:+91 4442116715" className="text-green-600 hover:text-green-800 transition-colors font-medium">
              +91 4442116715
            </a>
          </div>
        </div>

        <div className="flex items-start space-x-4 p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
            <FaClock className="text-white text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-gray-900 mb-1">Business Hours</h3>
            <p className="text-gray-600 text-sm mb-2">We're available during these times</p>
            <div className="text-purple-600 font-medium text-sm">
              <p>Mon-Fri: 9:00 AM - 6:00 PM</p>
              <p>Sat & Sun: Closed</p>
            </div>
          </div>
        </div>

        <div className="flex items-start space-x-4 p-6 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl">
          <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
            <FaBolt className="text-white text-xl" />
          </div>
          <div>
            <h3 className="font-bold text-gray-900 mb-1">Response Time</h3>
            <p className="text-gray-600 text-sm mb-2">How quickly we get back to you</p>
            <div className="text-orange-600 font-medium text-sm">
              <p>Email: Within 24 hours</p>
              <p>Phone: Immediate</p>
            </div>
          </div>
        </div>
      </div>

      {/* Social Media */}
      <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6">
        <h3 className="font-bold text-gray-900 mb-4">Follow Us</h3>
        <div className="flex space-x-4">
          <a href="#" className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center text-white hover:bg-blue-600 transition-colors" aria-label="Follow us on Facebook">
            <FaFacebookF className="text-sm" />
          </a>
          <a href="#" className="w-10 h-10 bg-blue-400 rounded-lg flex items-center justify-center text-white hover:bg-blue-500 transition-colors" aria-label="Follow us on Twitter">
            <FaTwitter className="text-sm" />
          </a>
          <a href="#" className="w-10 h-10 bg-blue-700 rounded-lg flex items-center justify-center text-white hover:bg-blue-800 transition-colors" aria-label="Connect with us on LinkedIn">
            <FaLinkedinIn className="text-sm" />
          </a>
          <a href="#" className="w-10 h-10 bg-pink-500 rounded-lg flex items-center justify-center text-white hover:bg-pink-600 transition-colors" aria-label="Follow us on Instagram">
            <FaInstagram className="text-sm" />
          </a>
          <a href="#" className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center text-white hover:bg-red-600 transition-colors" aria-label="Subscribe to our YouTube channel">
            <FaYoutube className="text-sm" />
          </a>
        </div>
      </div>
    </div>
  );
};

export default QuickContactInfo;
