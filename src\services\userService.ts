import apiService, { ApiResponse } from './api';

// User interfaces
export interface User {
  id: number;
  name: string | null;
  email: string;
  phone: string | null;
  domain: string | null; // Note: API returns "domine" but we'll use "domain"
  role: 'INTERN' | 'MANAGER' | 'TL' | 'ADMIN';
  portfolioLink: string | null; // API: portolioLink
  githubLink: string | null; // API: gitProfile
  linkedinLink: string | null; // API: linkeinProfile
  skills: string[] | null;
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | null;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  phone: string;
  domain: string;
  role: 'INTERN' | 'MANAGER' | 'TL';
  portfolioLink: string;
  githubLink: string;
  linkedinLink: string;
  skills: string[];
  status: 'ACTIVE' | 'INACTIVE';
}

export interface UpdateUserRequest {
  name: string;
  email: string;
  phone: string;
  domain: string;
  role: 'INTERN' | 'MANAGER' | 'TL';
  portfolioLink: string;
  githubLink: string;
  linkedinLink: string;
  skills: string[];
  status: 'ACTIVE' | 'INACTIVE';
}

export interface UserCount {
  role: string;
  count: number;
}

export interface UserStatusCount {
  status: string | null;
  count: number;
}

export interface UsersResponse {
  message: string;
  type: 'INFO' | 'ERROR';
  data: {
    userCount: UserCount[];
    userCountStatus: UserStatusCount[];
    userDetails: Array<{
      id: number;
      name: string | null;
      email: string;
      status: 'ACTIVE' | 'INACTIVE' | 'PENDING' | null;
      role: 'INTERN' | 'MANAGER' | 'TL' | 'ADMIN';
      skills: string[] | null;
      domine: string | null;
      gitProfile: string | null;
      portolioLink: string | null;
      linkeinProfile: string | null;
      phone: string | null;
    }>;
  };
  status: number;
}



class UserService {
  // Get all users with dashboard data
  async getUsers(filters?: { text?: string; role?: string; status?: string }): Promise<UsersResponse> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters?.text && filters.text.trim()) {
        queryParams.append('text', filters.text.trim());
      }

      if (filters?.role && filters.role !== 'ALL') {
        queryParams.append('role', filters.role);
      }

      if (filters?.status && filters.status !== 'ALL') {
        queryParams.append('status', filters.status);
      }

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/admin/user?${queryString}` : '/admin/user';

      const response = await apiService.get<UsersResponse>(endpoint);
      return response;
    } catch (error: any) {
      console.error('Error fetching users:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch users');
    }
  }

  // Create new user
  async createUser(data: CreateUserRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post<ApiResponse>('/admin/user/create-user', data);
      return response;
    } catch (error: any) {
      console.error('Error creating user:', error);
      throw new Error(error.response?.data?.message || 'Failed to create user');
    }
  }

  // Update user
  async updateUser(id: number, data: UpdateUserRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post<ApiResponse>(`/admin/user/update-user/${id}`, data);
      return response;
    } catch (error: any) {
      console.error('Error updating user:', error);
      throw new Error(error.response?.data?.message || 'Failed to update user');
    }
  }

  // Delete user
  async deleteUser(id: number): Promise<ApiResponse> {
    try {
      const response = await apiService.delete<ApiResponse>(`/admin/user/delete-user/${id}`);
      return response;
    } catch (error: any) {
      console.error('Error deleting user:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete user');
    }
  }

  // Helper method to transform API user data to our User interface
  transformApiUser(apiUser: UsersResponse['data']['userDetails'][0]): User {
    return {
      id: apiUser.id,
      name: apiUser.name,
      email: apiUser.email,
      phone: apiUser.phone,
      domain: apiUser.domine, // Transform API field name
      role: apiUser.role,
      portfolioLink: apiUser.portolioLink, // Transform API field name
      githubLink: apiUser.gitProfile, // Transform API field name
      linkedinLink: apiUser.linkeinProfile, // Transform API field name
      skills: apiUser.skills,
      status: apiUser.status,
    };
  }

  // Get role options for dropdowns
  getRoleOptions() {
    return [
      { value: 'INTERN', label: 'Intern' },
      { value: 'MANAGER', label: 'Manager' },
      { value: 'TL', label: 'Team Leader' }
    ];
  }

  // Get status options for dropdowns
  getStatusOptions() {
    return [
      { value: 'ACTIVE', label: 'Active' },
      { value: 'INACTIVE', label: 'Inactive' }
    ];
  }

  // Validate user data
  validateUserData(data: Partial<CreateUserRequest | UpdateUserRequest>): string[] {
    const errors: string[] = [];

    if (!data.name?.trim()) {
      errors.push('Name is required');
    }

    if (!data.email?.trim()) {
      errors.push('Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('Please enter a valid email address');
    }

    if (!data.phone?.trim()) {
      errors.push('Phone number is required');
    }

    if (!data.domain?.trim()) {
      errors.push('Domain is required');
    }

    if (!data.role) {
      errors.push('Role is required');
    }

    if (!data.status) {
      errors.push('Status is required');
    }

    if (!data.skills || data.skills.length === 0) {
      errors.push('At least one skill is required');
    }

    return errors;
  }
}

const userService = new UserService();
export default userService;
