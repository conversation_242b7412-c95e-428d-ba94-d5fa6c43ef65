import React, { createContext, useContext, useEffect, useRef, useState, ReactNode } from 'react';
import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';

// WebSocket message types
export interface WebSocketMessage {
  message: string;
  type: 'INFO' | 'ERROR' | 'WARNING';
  data: any;
  event: 'ADD' | 'UPDATE' | 'DELETE';
  field: 'USER' | 'PROJECT' | 'CONTACT' | 'APPLICATION' | 'TEAM';
  status: number;
}

// Context interface
interface WebSocketContextType {
  isConnected: boolean;
  lastMessage: WebSocketMessage | null;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  subscribe: (callback: (message: WebSocketMessage) => void) => () => void;
  unsubscribe: (callback: (message: WebSocketMessage) => void) => void;
  reconnect: () => void;
  reconnectWithNewToken: () => void;
}

// Create context
const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

// Provider props
interface WebSocketProviderProps {
  children: ReactNode;
  url?: string;
}

// WebSocket Provider Component
export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ 
  children, 
  url = import.meta.env.VITE_API_BASE_URL + '/ws/admin'
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  
  const stompClientRef = useRef<Client | null>(null);
  const subscribersRef = useRef<Set<(message: WebSocketMessage) => void>>(new Set());
  const reconnectTimeoutRef = useRef<number | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  // Initialize WebSocket connection
  const connect = () => {
    if (stompClientRef.current?.connected) {
      console.log('🔌 WebSocket already connected');
      return;
    }

    setConnectionStatus('connecting');
    console.log('🔌 Connecting to WebSocket...');

    try {
      // Ensure global polyfill is available
      if (typeof (window as any).global === 'undefined') {
        (window as any).global = globalThis;
      }

      // Create SockJS socket
      const socket = new SockJS(url);
      
      // Get auth token from localStorage
      const token = localStorage.getItem('admin_token');

      // Create STOMP client
      const stompClient = new Client({
        webSocketFactory: () => socket,
        debug: (str) => {
          console.log('🔌 STOMP Debug:', str);
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
        connectHeaders: token ? {
          Authorization: `Bearer ${token}`
        } : {},
      });

      // Connection established
      stompClient.onConnect = (frame) => {
        console.log('✅ WebSocket Connected:', frame);
        console.log('🔐 Authentication successful with token');
        setIsConnected(true);
        setConnectionStatus('connected');
        reconnectAttemptsRef.current = 0;

        stompClient.subscribe('/topic/admin', (message) => {
          try {
            console.log('📩 Raw WebSocket message:', message.body);
            const parsedMessage: WebSocketMessage = JSON.parse(message.body);
            console.log('📩 Received WebSocket message:', parsedMessage);

            setLastMessage(parsedMessage);

            // Notify all subscribers
            subscribersRef.current.forEach(callback => {
              try {
                callback(parsedMessage);
              } catch (error) {
                console.error('❌ Error in subscriber callback:', error);
              }
            });
          } catch (error) {
            console.error('❌ Error parsing WebSocket message:', error);
          }
        }, { Authorization: `Bearer ${token}` });
      };

      // Connection error
      stompClient.onStompError = (frame) => {
        console.error('❌ STOMP Error:', frame.headers['message']);
        console.error('❌ Additional details:', frame.body);

        // Check for authentication errors
        const errorMessage = frame.headers['message'] || '';
        if (errorMessage.includes('Unauthorized') || errorMessage.includes('401') || errorMessage.includes('403')) {
          console.error('🔐 Authentication failed - invalid or expired token');
          // Don't attempt to reconnect on auth errors
          setIsConnected(false);
          setConnectionStatus('error');
          return;
        }

        setIsConnected(false);
        setConnectionStatus('error');
        handleReconnect();
      };

      // Connection closed
      stompClient.onDisconnect = () => {
        console.log('🔌 WebSocket Disconnected');
        setIsConnected(false);
        setConnectionStatus('disconnected');
        handleReconnect();
      };

      // WebSocket error
      stompClient.onWebSocketError = (error) => {
        console.error('❌ WebSocket Error:', error);
        setConnectionStatus('error');
        handleReconnect();
      };

      stompClientRef.current = stompClient;
      stompClient.activate();

    } catch (error) {
      console.error('❌ Failed to create WebSocket connection:', error);
      setConnectionStatus('error');
      handleReconnect();
    }
  };

  // Handle reconnection logic
  const handleReconnect = () => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      console.log('❌ Max reconnection attempts reached');
      setConnectionStatus('error');
      return;
    }

    reconnectAttemptsRef.current++;
    const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
    
    console.log(`🔄 Attempting to reconnect in ${delay}ms (attempt ${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  };

  // Manual reconnect function
  const reconnect = () => {
    reconnectAttemptsRef.current = 0;
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    if (stompClientRef.current) {
      stompClientRef.current.deactivate();
    }

    setTimeout(() => {
      connect();
    }, 1000);
  };

  // Reconnect with new token (useful when token is refreshed)
  const reconnectWithNewToken = () => {
    console.log('🔐 Reconnecting with updated token...');
    reconnect();
  };

  // Subscribe to messages
  const subscribe = (callback: (message: WebSocketMessage) => void) => {
    subscribersRef.current.add(callback);
    
    // Return unsubscribe function
    return () => {
      subscribersRef.current.delete(callback);
    };
  };

  // Unsubscribe from messages
  const unsubscribe = (callback: (message: WebSocketMessage) => void) => {
    subscribersRef.current.delete(callback);
  };

  // Monitor token changes and reconnect if needed
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth_token' && e.newValue !== e.oldValue) {
        console.log('🔐 Auth token changed, reconnecting WebSocket...');
        if (stompClientRef.current?.connected) {
          reconnectWithNewToken();
        }
      }
    };

    // Listen for localStorage changes (from other tabs)
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Initialize connection on mount
  useEffect(() => {
    connect();

    // Cleanup on unmount
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      if (stompClientRef.current) {
        console.log('🔌 Disconnecting WebSocket...');
        stompClientRef.current.deactivate();
      }
    };
  }, [url]);

  // Context value
  const contextValue: WebSocketContextType = {
    isConnected,
    lastMessage,
    connectionStatus,
    subscribe,
    unsubscribe,
    reconnect,
    reconnectWithNewToken,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

// Custom hook to use WebSocket context
export const useWebSocket = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

// Hook for specific field updates
export const useWebSocketField = (field: WebSocketMessage['field']) => {
  const { subscribe } = useWebSocket();
  const [fieldMessage, setFieldMessage] = useState<WebSocketMessage | null>(null);

  useEffect(() => {
    const unsubscribe = subscribe((message) => {
      if (message.field === field) {
        setFieldMessage(message);
      }
    });

    return unsubscribe;
  }, [field, subscribe]);

  return fieldMessage;
};

export default WebSocketContext;
