import React, { useState, useEffect } from 'react';
import {
  ContactsHeader,
  ContactsStats,
  ContactsFilter,
  ContactsTable
} from '../../components/admin-contacts';
import { ContactDetailModal, ContactEditModal } from '../../components/admin';
import { Contact } from '../../types/contact';
import useContacts from '../../hooks/useContacts';

const AdminContacts: React.FC = () => {
  const {
    contacts,
    stats,
    loading,
    filters,
    createContact,
    updateContact,
    deleteContact,
    addNote,
    deleteNote,
    updateFilters,
    applyFilters,
    resetFilters
  } = useContacts();
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Update selected contact when contacts list changes (after adding notes, etc.)
  useEffect(() => {
    if (selectedContact) {
      const updatedContact = contacts.find(c => c.id === selectedContact.id);
      if (updatedContact) {
        setSelectedContact(updatedContact);
      }
    }
  }, [contacts, selectedContact?.id]);

  // For now, show all contacts (filtering can be added later)
  const filteredContacts = contacts;



  const handleViewContact = (contact: Contact) => {
    setSelectedContact(contact);
    setIsDetailModalOpen(true);
  };

  const handleEditContact = (contact: Contact) => {
    setSelectedContact(contact);
    setIsEditModalOpen(true);
  };

  const handleDeleteContact = async (contactId: number) => {
    if (confirm('Are you sure you want to delete this contact? This action cannot be undone.')) {
      try {
        await deleteContact(contactId);
        // Close modal if the deleted contact was being viewed
        if (selectedContact?.id === contactId) {
          setIsDetailModalOpen(false);
          setSelectedContact(null);
        }
      } catch (error) {
        console.error('Failed to delete contact:', error);
        alert('Failed to delete contact. Please try again.');
      }
    }
  };





  return (
    <div className="space-y-6">
      {/* Page Header */}
      <ContactsHeader onAddContact={() => {
        setSelectedContact(null);
        setIsEditModalOpen(true);
      }} />

      {/* Stats Cards */}
      <ContactsStats stats={stats} />

      {/* Filter Component */}
      <ContactsFilter
        filters={filters}
        onFiltersChange={updateFilters}
        onApplyFilters={applyFilters}
        onResetFilters={resetFilters}
        loading={loading}
      />

      {/* Contacts Table */}
      <ContactsTable
        contacts={filteredContacts}
        onViewContact={handleViewContact}
        onEditContact={handleEditContact}
        onDeleteContact={handleDeleteContact}
      />

      {/* Contact Detail Modal */}
      {isDetailModalOpen && selectedContact && (
        <ContactDetailModal
          contact={selectedContact}
          isOpen={isDetailModalOpen}
          onClose={() => setIsDetailModalOpen(false)}
          onUpdateContact={async (updatedContact) => {
            try {
              await updateContact(updatedContact.id, {
                name: updatedContact.name,
                email: updatedContact.email,
                phone: updatedContact.phone,
                type: updatedContact.type,
                subject: updatedContact.subject,
                message: updatedContact.message,
                status: updatedContact.status,
                priority: updatedContact.priority,
                source: updatedContact.source
              });
              setSelectedContact(updatedContact);
            } catch (error) {
              console.error('Failed to update contact:', error);
            }
          }}
          onAddNote={async (contactId, notes) => {
            try {
              await addNote(contactId, notes);
              // Update the selected contact with fresh data
              const updatedContact = contacts.find(c => c.id === contactId);
              if (updatedContact) {
                setSelectedContact(updatedContact);
              }
            } catch (error) {
              console.error('Failed to add note:', error);
            }
          }}
          onDeleteNote={async (contactId, noteId) => {
            try {
              await deleteNote(contactId, noteId);
              // Update the selected contact with fresh data
              const updatedContact = contacts.find(c => c.id === contactId);
              if (updatedContact) {
                setSelectedContact(updatedContact);
              }
            } catch (error) {
              console.error('Failed to delete note:', error);
            }
          }}
        />
      )}

      {/* Edit Contact Modal */}
      {isEditModalOpen && (
        <ContactEditModal
          contact={selectedContact}
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedContact(null);
          }}
          onSaveContact={async (contact) => {
            try {
              if (selectedContact) {
                // Update existing contact
                await updateContact(contact.id, {
                  name: contact.name,
                  email: contact.email,
                  phone: contact.phone,
                  type: contact.type,
                  subject: contact.subject,
                  message: contact.message,
                  status: contact.status,
                  priority: contact.priority,
                  source: contact.source
                });
              } else {
                // Create new contact
                await createContact({
                  name: contact.name,
                  email: contact.email,
                  phone: contact.phone,
                  type: contact.type,
                  subject: contact.subject,
                  message: contact.message
                });
              }
              setIsEditModalOpen(false);
              setSelectedContact(null);
            } catch (error) {
              console.error('Failed to save contact:', error);
            }
          }}
        />
      )}
    </div>
  );
};

export default AdminContacts;
