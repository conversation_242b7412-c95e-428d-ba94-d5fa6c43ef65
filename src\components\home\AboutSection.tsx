import React from 'react';
import { Card, CardContent } from '../ui';

const AboutSection: React.FC = () => {
  const features = [
    {
      icon: '🎯',
      title: 'Industry-Focused Learning',
      description: 'Immerse yourself in real-world projects that mirror Fortune 500 company challenges. Our curriculum is designed by industry veterans to ensure you\'re solving actual problems that companies face today.',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      icon: '👨‍🏫',
      title: 'Elite Mentorship Program',
      description: 'Learn directly from senior engineers at Google, Meta, Netflix, and other tech giants. Get personalized guidance, code reviews, and career advice from professionals who\'ve built systems used by millions.',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      icon: '🏆',
      title: 'Global Recognition',
      description: 'Earn blockchain-verified certificates from prestigious institutions across three continents. Our certifications are recognized by top tech companies and significantly boost your hiring potential.',
      gradient: 'from-yellow-500 to-orange-500'
    },
    {
      icon: '🚀',
      title: 'Career Transformation',
      description: 'Transform from student to industry professional with our comprehensive career acceleration program. Build a portfolio that showcases enterprise-level skills and attracts top employers.',
      gradient: 'from-green-500 to-teal-500'
    },
    {
      icon: '🤝',
      title: 'Global Tech Network',
      description: 'Join an exclusive community of developers, entrepreneurs, and tech leaders spanning 25+ countries. Access lifetime networking opportunities and collaborative projects.',
      gradient: 'from-indigo-500 to-purple-500'
    },
    {
      icon: '📈',
      title: 'Cutting-Edge Technologies',
      description: 'Master the latest technologies and frameworks before they become mainstream. Stay ahead of the curve with early access to emerging tools and industry best practices.',
      gradient: 'from-pink-500 to-red-500'
    }
  ];

  return (
    <section id="about" className="py-24 bg-gradient-to-br from-gray-50 via-white to-purple-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-purple-300/20 to-pink-300/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-blue-300/20 to-cyan-300/20 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-block mb-6">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-full text-sm font-bold tracking-wide">
              ✨ THE MSS LAB ADVANTAGE
            </span>
          </div>
          <h2 className="text-4xl md:text-6xl font-black text-gray-900 mb-8">
            Why Top Companies
            <span className="block gradient-text-primary">Choose Our Alumni</span>
          </h2>
          <p className="text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            We don't just teach coding - we forge the next generation of tech leaders. Our comprehensive approach
            transforms ambitious students into <span className="gradient-text-secondary font-bold">industry-ready professionals</span>
            who drive innovation at the world's leading companies.
          </p>
        </div>

        {/* Enhanced Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <Card key={index} hover glass className="text-center group relative overflow-hidden">
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>
              <CardContent className="pt-8 pb-8 relative">
                <div className="text-6xl mb-6 transform group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:gradient-text-primary transition-all duration-300">
                  {feature.title}
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Enhanced Mission Statement */}
        <div className="relative">
          <div className="glass rounded-3xl p-12 md:p-16 relative overflow-hidden">
            {/* Background gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-cyan-500/10"></div>

            <div className="relative max-w-5xl mx-auto text-center">
              <div className="mb-8">
                <span className="text-6xl mb-6 block">🚀</span>
                <h3 className="text-3xl md:text-5xl font-black text-gray-900 mb-8">
                  Our <span className="gradient-text-primary">Revolutionary</span> Mission
                </h3>
              </div>

              <p className="text-xl md:text-2xl text-gray-800 leading-relaxed mb-12 font-light">
                At <span className="font-bold gradient-text-secondary">MSS Internship (Mothercode Software Systems)</span>,
                we're not just another coding bootcamp. We're a <span className="font-bold">transformation engine</span> that
                takes passionate students and forges them into the tech leaders of tomorrow. Our mission is to eliminate
                the gap between academic theory and industry reality, creating a seamless bridge that launches careers
                and changes lives.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mt-16">
                <div className="glass rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                  <div className="text-4xl font-black gradient-text-primary mb-3">25+</div>
                  <div className="text-gray-700 font-semibold">Years of Innovation</div>
                  <div className="text-gray-500 text-sm mt-1">Pioneering Excellence</div>
                </div>
                <div className="glass rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                  <div className="text-4xl font-black gradient-text-secondary mb-3">50+</div>
                  <div className="text-gray-700 font-semibold">Global Partners</div>
                  <div className="text-gray-500 text-sm mt-1">Fortune 500 Companies</div>
                </div>
                <div className="glass rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                  <div className="text-4xl font-black gradient-text-accent mb-3">100%</div>
                  <div className="text-gray-700 font-semibold">Hands-On Learning</div>
                  <div className="text-gray-500 text-sm mt-1">Real Project Experience</div>
                </div>
                <div className="glass rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                  <div className="text-4xl font-black gradient-text-primary mb-3">∞</div>
                  <div className="text-gray-700 font-semibold">Lifetime Support</div>
                  <div className="text-gray-500 text-sm mt-1">Alumni Network</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
