import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from '../components/layout';
import { ContactForm, QuickContactInfo, FAQ } from '../components/contact';

const Contact: React.FC = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Structured Data for Contact Page */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "ContactPage",
          "name": "Contact MSS Internship",
          "description": "Get in touch with MSS Internship for internship inquiries and support",
          "mainEntity": {
            "@type": "Organization",
            "name": "MSS Internship - Mothercode Software Systems",
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "+91 4442116715",
              "contactType": "customer service",
              "email": "<EMAIL>",
              "availableLanguage": ["English"]
            }
          }
        })}
      </script>
      <Header />

      {/* Contact Form and Info */}
      <main id="main-content" role="main">
      <section className="py-20 bg-white" aria-label="Contact information and form">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            {/* Contact Form */}
            <div>
              <ContactForm />
            </div>
            
            {/* Quick Contact Info */}
            <QuickContactInfo />
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQ />
      </main>

      <Footer />
    </div>
  );
};

export default Contact;
