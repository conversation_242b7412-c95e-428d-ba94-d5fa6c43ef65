import { useState, useMemo } from 'react';
import { Project } from '../types/projects';

export const useProjectsFilters = (projects: Project[]) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredProjects = useMemo(() => {
    return projects.filter(project => {
      const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()));
      
      
      return matchesSearch ;
    });
  }, [projects, searchTerm]);

  const clearFilters = () => {
    setSearchTerm('');
  };

  return {
    searchTerm,
    setSearchTerm,
    filteredProjects,
    clearFilters
  };
};
