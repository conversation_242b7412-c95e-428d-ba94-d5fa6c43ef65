import { Project } from '../types';

export const projects: Project[] = [
  {
    id: '1',
    title: 'E-commerce Platform Development',
    description: 'Build a modern e-commerce platform with React, Node.js, and MongoDB. Features include user authentication, product catalog, shopping cart, payment integration, and admin dashboard.',
    requirements: [
      'Basic knowledge of React and JavaScript',
      'Understanding of REST APIs',
      'Familiarity with databases',
      'Git version control experience'
    ],
    duration: '3 months',
    type: 'fullstack',
    technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'TypeScript', 'Tailwind CSS'],
    mentorName: '<PERSON>',
    openPositions: 3,
    applicationDeadline: '2025-08-15',
    isActive: true
  },
  {
    id: '2',
    title: 'Mobile App for Task Management',
    description: 'Develop a cross-platform mobile application for task and project management using React Native. Include features like task creation, team collaboration, notifications, and offline sync.',
    requirements: [
      'JavaScript/TypeScript proficiency',
      'Basic understanding of mobile development',
      'Knowledge of React concepts',
      'Problem-solving skills'
    ],
    duration: '4 months',
    type: 'mobile',
    technologies: ['React Native', 'TypeScript', 'Firebase', 'Redux', 'Expo'],
    mentorName: '<PERSON>',
    openPositions: 2,
    applicationDeadline: '2025-08-20',
    isActive: true
  },
  {
    id: '3',
    title: 'AI-Powered Chatbot Development',
    description: 'Create an intelligent chatbot using natural language processing and machine learning. The bot will handle customer support queries and provide automated responses.',
    requirements: [
      'Python programming experience',
      'Basic understanding of machine learning',
      'Knowledge of APIs and web services',
      'Interest in AI/NLP technologies'
    ],
    duration: '3 months',
    type: 'ai/ml',
    technologies: ['Python', 'TensorFlow', 'Flask', 'OpenAI API', 'Docker', 'PostgreSQL'],
    mentorName: 'Dr. Priya Sharma',
    openPositions: 2,
    applicationDeadline: '2025-08-10',
    isActive: true
  },
  {
    id: '4',
    title: 'DevOps Pipeline Automation',
    description: 'Build and maintain CI/CD pipelines for automated testing, deployment, and monitoring of applications. Learn about containerization, orchestration, and cloud infrastructure.',
    requirements: [
      'Basic Linux command line knowledge',
      'Understanding of software development lifecycle',
      'Interest in automation and infrastructure',
      'Problem-solving mindset'
    ],
    duration: '3 months',
    type: 'devops',
    technologies: ['Docker', 'Kubernetes', 'Jenkins', 'AWS', 'Terraform', 'Monitoring Tools'],
    mentorName: 'Alex Rodriguez',
    openPositions: 2,
    applicationDeadline: '2025-08-25',
    isActive: true
  },
  {
    id: '5',
    title: 'Frontend Dashboard for Analytics',
    description: 'Design and develop a responsive dashboard for data visualization and analytics. Create interactive charts, real-time data updates, and user-friendly interfaces.',
    requirements: [
      'Strong HTML, CSS, JavaScript skills',
      'Experience with React or similar framework',
      'Understanding of data visualization concepts',
      'Eye for design and user experience'
    ],
    duration: '2 months',
    type: 'frontend',
    technologies: ['React', 'TypeScript', 'D3.js', 'Chart.js', 'Tailwind CSS', 'REST APIs'],
    mentorName: 'Emma Wilson',
    openPositions: 4,
    applicationDeadline: '2025-09-01',
    isActive: true
  },
  {
    id: '6',
    title: 'Backend API Development',
    description: 'Build robust RESTful APIs and microservices architecture. Focus on scalability, security, and performance optimization for high-traffic applications.',
    requirements: [
      'Proficiency in Node.js or Python',
      'Database design knowledge',
      'Understanding of API design principles',
      'Basic knowledge of cloud services'
    ],
    duration: '3 months',
    type: 'backend',
    technologies: ['Node.js', 'Express', 'PostgreSQL', 'Redis', 'JWT', 'AWS'],
    mentorName: 'David Kim',
    openPositions: 3,
    applicationDeadline: '2025-08-30',
    isActive: true
  }
];
