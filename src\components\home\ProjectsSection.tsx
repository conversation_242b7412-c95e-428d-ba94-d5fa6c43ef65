import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useProjects } from '../../hooks';
import ProjectCard from './ProjectCard';
import { Button, LoadingSpinner } from '../ui';
import { FaRocket } from 'react-icons/fa';

const ProjectsSection: React.FC = () => {
  const navigate = useNavigate();
  const { projects, loading, error } = useProjects();

  // Show only first 3 projects on home page
  const featuredProjects = projects.slice(0, 3);

  const handleViewDetails = (projectId: string) => {
    navigate(`/project/${projectId}`);
  };

  const handleViewAllProjects = () => {
    navigate('/projects');
  };

  if (loading) {
    return (
      <section
        id="projects"
        className="py-24 bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50 relative overflow-hidden"
        aria-label="Featured internship projects"
        role="region"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex flex-col items-center space-y-6">
              <LoadingSpinner size="xl" color="primary" />
              <div className="space-y-2">
                <p className="text-2xl font-bold gradient-text-primary">Loading Amazing Projects...</p>
                <p className="text-gray-600">Preparing your next career opportunity</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id="projects" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-red-600">{error}</p>
            <Button className="mt-4" onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="projects" className="py-24 bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-purple-200/30 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-pink-200/30 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-block mb-4">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-bold">
              🎯 LIVE PROJECTS
            </span>
          </div>
          <h2 className="text-4xl md:text-6xl font-black text-gray-900 mb-6">
            Transform Your Skills with
            <span className="block gradient-text-primary">Real-World Projects</span>
          </h2>
          <p className="text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            Dive into cutting-edge projects designed by industry experts. Each project is a gateway to
            <span className="gradient-text-secondary font-semibold"> professional excellence</span> and career transformation.
          </p>
        </div>

        {/* Featured Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {featuredProjects.map(project => (
            <ProjectCard
              key={project.id}
              project={project}
              onViewDetails={handleViewDetails}
            />
          ))}
        </div>

        {/* View All Projects Button */}
        <div className="text-center">
          <div className="mb-8">
            <p className="text-lg text-gray-600 mb-4">
              Showing <span className="font-bold text-purple-600">{featuredProjects.length}</span> of <span className="font-bold">{projects.length}</span> available projects
            </p>
            <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
              <span className="flex items-center space-x-1">
                <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                <span>{projects.filter(p => p.status === 'ACTIVE').length} Open Positions</span>
              </span>
              <span>•</span>
              <span>Multiple Difficulty Levels</span>
              <span>•</span>
              <span>Industry Partners</span>
            </div>
          </div>

          <Button
            onClick={handleViewAllProjects}
            variant="gradient"
            size="lg"
            glow
            className="px-12 py-4 text-lg font-bold"
          >
            <span className="flex items-center space-x-2">
              <FaRocket />
              <span>View All Projects</span>
            </span>
          </Button>

          <p className="text-sm text-gray-500 mt-4">
            Explore advanced filtering, search, and detailed project information
          </p>
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;
