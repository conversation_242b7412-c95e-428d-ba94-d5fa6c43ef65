import apiService, { ApiResponse } from "./api";


// Team interfaces
export interface TeamUser {
  role: 'INTERN' | 'TL' | 'MANAGER';
  name: string;
}

export interface TeamProject {
  projectName: string;
  projectId: number;
}

export interface Team {
  teamId: number;
  title: string;
  teamStatus: 'ACTIVE' | 'INACTIVE';
  description: string;
  users: TeamUser[];
  projects: TeamProject[];
}

export interface User {
  name: string;
  id: number;
  role: 'INTERN' | 'TL' | 'MANAGER';
}

export interface Project {
  projectName: string;
  projectId: number;
}

export interface TeamStatusCount {
  activeCount: number;
  inActiveCount: number;
}

export interface UserCount {
  internCount: number;
  managerCount: number;
  tlCoount: number; // Note: API has typo "tlCoount"
}

export interface TeamDetailsResponse {
  teamStatusCount: TeamStatusCount;
  teamDetails: Team[];
  projects: Project[];
  userCount: UserCount;
  users: User[];
}

export interface CreateTeamRequest {
  teamName: string;
  teamDescription: string;
  teamStatus: 'ACTIVE' | 'INACTIVE';
  userIds: number[];
  projectIds: number[];
}

export interface UpdateTeamRequest {
  teamName: string;
  teamDescription: string;
  teamStatus: 'ACTIVE' | 'INACTIVE';
  userIds: number[];
  projectIds: number[];
}


class TeamService {
  // Get team details
  async getTeamDetails(filters?: { teamTitle?: string; projectId?: string; status?: string }): Promise<{ data: TeamDetailsResponse }> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters?.teamTitle && filters.teamTitle.trim()) {
        queryParams.append('teamTitle', filters.teamTitle.trim());
      }

      if (filters?.projectId && filters.projectId !== 'ALL') {
        queryParams.append('projectId', filters.projectId);
      }

      if (filters?.status && filters.status !== 'ALL') {
        queryParams.append('status', filters.status);
      }

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/admin/teams/details?${queryString}` : '/admin/teams/details';

      const response = await apiService.get<{ data: TeamDetailsResponse }>(endpoint);
      return response;
    } catch (error: any) {
      console.error('Error fetching team details:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch team details');
    }
  }

  // Create team
  async createTeam(teamData: CreateTeamRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post<ApiResponse>('/admin/teams/create', teamData);
      return response;
    } catch (error: any) {
      console.error('Error creating team:', error);
      throw new Error(error.response?.data?.message || 'Failed to create team');
    }
  }

  // Update team
  async updateTeam(id: number, teamData: UpdateTeamRequest): Promise<ApiResponse> {
    try {
      const response = await apiService.post<ApiResponse>(`/admin/teams/update/${id}`, teamData);
      return response;
    } catch (error: any) {
      console.error('Error updating team:', error);
      throw new Error(error.response?.data?.message || 'Failed to update team');
    }
  }

  // Delete team
  async deleteTeam(id: number): Promise<ApiResponse> {
    try {
      const response = await apiService.delete<ApiResponse>(`/admin/teams/delete/${id}`);
      return response;
    } catch (error: any) {
      console.error('Error deleting team:', error);
      throw new Error(error.response?.data?.message || 'Failed to delete team');
    }
  }

  // Helper method to get team status display name
  getTeamStatusDisplayName(status: 'ACTIVE' | 'INACTIVE'): string {
    switch (status) {
      case 'ACTIVE':
        return 'Active';
      case 'INACTIVE':
        return 'Inactive';
      default:
        return status;
    }
  }

  // Helper method to get role display name
  getRoleDisplayName(role: 'INTERN' | 'TL' | 'MANAGER'): string {
    switch (role) {
      case 'INTERN':
        return 'Intern';
      case 'TL':
        return 'Team Leader';
      case 'MANAGER':
        return 'Manager';
      default:
        return role;
    }
  }

  // Helper method to filter users by role
  filterUsersByRole(users: User[], role: 'INTERN' | 'TL' | 'MANAGER'): User[] {
    return users.filter(user => user.role === role);
  }

  // Helper method to get users by IDs
  getUsersByIds(users: User[], userIds: number[]): User[] {
    return users.filter(user => userIds.includes(user.id));
  }

  // Helper method to get projects by IDs
  getProjectsByIds(projects: Project[], projectIds: number[]): Project[] {
    return projects.filter(project => projectIds.includes(project.projectId));
  }
}

// Create and export singleton instance
export const teamService = new TeamService();
export default teamService;
