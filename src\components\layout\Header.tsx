import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '../ui';
import PublicWebSocketStatus from '../common/PublicWebSocketStatus';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const headerRef = useRef<HTMLElement>(null);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (headerRef.current && !headerRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false);
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  // const scrollToSection = (sectionId: string) => {
  //   if (location.pathname !== '/') {
  //     // If not on home page, navigate to home first
  //     window.location.href = `/#${sectionId}`;
  //   } else {
  //     // If on home page, scroll to section
  //     const element = document.getElementById(sectionId);
  //     element?.scrollIntoView({ behavior: 'smooth' });
  //   }
  // };

  return (
    <header
      ref={headerRef}
      className="bg-gradient-to-r from-purple-900/95 via-indigo-900/95 to-purple-900/95 backdrop-blur-md shadow-2xl border-b border-white/10 sticky top-0 z-50"
      role="banner"
      aria-label="Main navigation"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center h-20 relative">
          {/* Logo and Brand */}
          <Link to="/" className="flex items-center space-x-4" aria-label="MSS Internship Homepage">
            <div className="flex-shrink-0 flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow" role="img" aria-label="MSS Internship Logo">
                <span className="text-white font-bold text-xl">M</span>
              </div>
              <div>
                {/* Mobile: MSS above Internship */}
                <div className="md:hidden">
                  <h1 className="text-2xl font-bold gradient-text-accent leading-tight">MSS</h1>
                  <h2 className="text-lg font-bold gradient-text-accent leading-tight">Labs</h2>
                </div>
                {/* Desktop: MSS Internship in one line */}
                <div className="hidden md:block">
                  <h1 className="text-3xl font-bold gradient-text-accent">MSS Labs</h1>
                </div>
              </div>
            </div>
          </Link>

          {/* Centered Navigation */}
        

          {/* Right side - Apply Now button and Mobile menu */}
          <div className="ml-auto flex items-center space-x-4">
              <nav className="hidden md:flex space-x-8" role="navigation" aria-label="Main navigation menu">
                  <Link
              to="/"
              className="text-white/90 hover:text-cyan-300 transition-all duration-300 font-medium hover:scale-110 relative group"
            >
              Home
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link
              to="/projects"
              className="text-white/90 hover:text-cyan-300 transition-all duration-300 font-medium hover:scale-110 relative group"
            >
              Projects
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link
              to="/contact"
              className="text-white/90 hover:text-cyan-300 transition-all duration-300 font-medium hover:scale-110 relative group"
            >
              Contact
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link
              to="/track"
              className="text-white/90 hover:text-cyan-300 transition-all duration-300 font-medium hover:scale-110 relative group"
            >
              Track
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 group-hover:w-full transition-all duration-300"></span>
            </Link>
          </nav>

            {/* WebSocket Status - Hidden on mobile, only shows when context available */}
            <div className="hidden md:block">
              <PublicWebSocketStatus />
            </div>

            {/* CTA Button - Hidden on mobile */}
            <div className="hidden md:block">
              <Button
                variant="gradient"
                size="sm"
                glow
                onClick={() => navigate('/projects')}
              >
                Apply Now
              </Button>
            </div>

            {/* Mobile Apply Now button */}
            <div className="md:hidden">
              <Button
                variant="gradient"
                size="sm"
                glow
                onClick={() => navigate('/projects')}
                className="text-xs px-3 py-1.5"
              >
                Apply Now
              </Button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={toggleMobileMenu}
                className="text-white/90 hover:text-cyan-300 transition-colors p-2"
                aria-label="Toggle mobile menu"
              >
                {isMobileMenuOpen ? (
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-gradient-to-b from-purple-900/98 to-indigo-900/98 backdrop-blur-md border-t border-white/10">
          <div className="px-4 pt-4 pb-6 space-y-4">
            <Link
              to="/projects"
              onClick={closeMobileMenu}
              className="block text-white/90 hover:text-cyan-300 transition-all duration-300 font-medium py-3 px-4 rounded-lg hover:bg-white/10"
            >
              Projects
            </Link>
            <Link
              to="/contact"
              onClick={closeMobileMenu}
              className="block text-white/90 hover:text-cyan-300 transition-all duration-300 font-medium py-3 px-4 rounded-lg hover:bg-white/10"
            >
              Contact
            </Link>
            <Link
              to="/track"
              onClick={closeMobileMenu}
              className="block text-white/90 hover:text-cyan-300 transition-all duration-300 font-medium py-3 px-4 rounded-lg hover:bg-white/10"
            >
              Track Application
            </Link>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
