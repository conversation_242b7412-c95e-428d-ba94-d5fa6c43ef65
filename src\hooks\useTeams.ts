import { useState, useEffect } from 'react';
import { teamService, CreateTeamRequest, UpdateTeamRequest, Team, User, Project } from '../services/teamService';
import { useWebSocketField } from '../contexts/WebSocketContext';

interface TeamFilters {
  teamTitle?: string;
  projectId?: string;
  status?: string;
}

interface UseTeamsReturn {
  teams: Team[];
  users: User[];
  projects: Project[];
  teamStatusCount: { activeCount: number; inActiveCount: number };
  userCount: { internCount: number; managerCount: number; tlCoount: number };
  loading: boolean;
  error: string | null;
  filters: TeamFilters;
  refreshTeams: () => Promise<void>;
  createTeam: (teamData: CreateTeamRequest) => Promise<void>;
  updateTeam: (id: number, teamData: UpdateTeamRequest) => Promise<void>;
  deleteTeam: (id: number) => Promise<void>;
  updateFilters: (newFilters: Partial<TeamFilters>) => void;
  applyFilters: () => Promise<void>;
  resetFilters: () => Promise<void>;
}

export const useTeams = (): UseTeamsReturn => {
  const [teams, setTeams] = useState<Team[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [teamStatusCount, setTeamStatusCount] = useState({ activeCount: 0, inActiveCount: 0 });
  const [userCount, setUserCount] = useState({ internCount: 0, managerCount: 0, tlCoount: 0 });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<TeamFilters>({
    teamTitle: '',
    projectId: 'ALL',
    status: 'ALL'
  });

  const fetchTeams = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await teamService.getTeamDetails(filters);
      
      setTeams(response.data.teamDetails);
      setUsers(response.data.users);
      setProjects(response.data.projects);
      setTeamStatusCount(response.data.teamStatusCount);
      setUserCount(response.data.userCount);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch teams');
      console.error('Error fetching teams:', err);
    } finally {
      setLoading(false);
    }
  };

  const refreshTeams = async () => {
    await fetchTeams();
  };

  const createTeam = async (teamData: CreateTeamRequest) => {
    try {
      await teamService.createTeam(teamData);
      await refreshTeams(); // Refresh the list after creating
    } catch (err: any) {
      setError(err.message || 'Failed to create team');
      throw err;
    }
  };

  const updateTeam = async (id: number, teamData: UpdateTeamRequest) => {
    try {
      await teamService.updateTeam(id, teamData);
      await refreshTeams(); // Refresh the list after updating
    } catch (err: any) {
      setError(err.message || 'Failed to update team');
      throw err;
    }
  };

  const deleteTeam = async (id: number) => {
    try {
      await teamService.deleteTeam(id);
      await refreshTeams(); // Refresh the list after deleting
    } catch (err: any) {
      setError(err.message || 'Failed to delete team');
      throw err;
    }
  };

  // Filter functions
  const updateFilters = (newFilters: Partial<TeamFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const applyFilters = async () => {
    await fetchTeams();
  };

  const resetFilters = async () => {
    // Reset filters to default state
    setFilters({ teamTitle: '', projectId: 'ALL', status: 'ALL' });
    // Immediately fetch teams without any filters
    try {
      setLoading(true);
      const response = await teamService.getTeamDetails();

      setTeams(response.data.teamDetails);
      setUsers(response.data.users);
      setProjects(response.data.projects);
      setTeamStatusCount(response.data.teamStatusCount);
      setUserCount(response.data.userCount);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load teams');
      console.error('Error fetching teams:', err);
    } finally {
      setLoading(false);
    }
  };

  // WebSocket integration for real-time updates
  const teamMessage = useWebSocketField('TEAM');

  useEffect(() => {
    if (teamMessage) {
      console.log('📩 Team WebSocket update:', teamMessage);

      switch (teamMessage.event) {
        case 'ADD':
          // Refresh teams to get the new team
          fetchTeams();
          break;
        case 'UPDATE':
          // Update specific team or refresh all
          if (teamMessage.data?.teams) {
            setTeams(teamMessage.data.teams);
          } else {
            fetchTeams();
          }
          break;
        case 'DELETE':
          // Refresh teams to remove deleted team
          fetchTeams();
          break;
        default:
          console.log('Unknown team event:', teamMessage.event);
      }
    }
  }, [teamMessage]);

  useEffect(() => {
    fetchTeams();
  }, []);

  return {
    teams,
    users,
    projects,
    teamStatusCount,
    userCount,
    loading,
    error,
    filters,
    refreshTeams,
    createTeam,
    updateTeam,
    deleteTeam,
    updateFilters,
    applyFilters,
    resetFilters,
  };
};
