import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, Input, ReCaptcha } from '../ui';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaPaperPlane } from 'react-icons/fa';
import contactService from '../../services/contactService';
import { useCaptcha } from '../../hooks';

interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  type: 'GENERAL' | 'INTERNSHIP' | 'PARTNERSHIP' | 'TECHNICAL';
}

const ContactForm: React.FC = () => {
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    type: 'GENERAL'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Captcha integration
  const {
    captchaToken,
    captchaError,
    isVerifying,
    isCaptchaEnabled,
    captchaRef,
    handleCaptchaVerify,
    handleCaptchaError,
    handleCaptchaExpired,
    resetCaptcha
  } = useCaptcha();

  // Get inquiry types from the contact service
  const inquiryTypes = contactService.getContactTypes();

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    if (!formData.subject.trim()) newErrors.subject = 'Subject is required';
    if (!formData.message.trim()) newErrors.message = 'Message is required';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Captcha validation (only in production)
    if (isCaptchaEnabled && !captchaToken) {
      newErrors.captcha = 'Please complete the captcha verification';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof ContactFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    setErrors({});

    try {
      // Use real API to create contact
      await contactService.createContact(formData);

      setIsSubmitted(true);

      // Reset form after success
      setTimeout(() => {
        setIsSubmitted(false);
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
          type: 'GENERAL'
        });
        resetCaptcha();
      }, 3000);
    } catch (error: any) {
      // Handle API errors
      setErrors({ general: error.message || 'Failed to send message. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <Card gradient className="max-w-2xl mx-auto">
        <CardContent className="text-center py-12">
          <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <FaCheck className="text-white text-3xl" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Message Sent Successfully!</h3>
          <p className="text-gray-600 mb-6">
            Thank you for contacting us. We'll get back to you within 24 hours.
          </p>
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4">
            <p className="text-sm text-gray-700">
              <strong>Reference ID:</strong> MSS-{Date.now().toString().slice(-6)}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card gradient className="max-w-2xl mx-auto">
      <CardHeader>
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Send us a Message</h2>
          <p className="text-gray-600">
            Fill out the form below and we'll get back to you as soon as possible
          </p>
        </div>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General Error Message */}
          {errors.general && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{errors.general}</p>
            </div>
          )}

          {/* Inquiry Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              What can we help you with? *
            </label>
            <select
              value={formData.type}
              onChange={(e) => handleInputChange('type', e.target.value as ContactFormData['type'])}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              {inquiryTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Personal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Full Name *"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={errors.name}
              placeholder="Enter your full name"
            />
            <Input
              label="Email Address *"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={errors.email}
              placeholder="<EMAIL>"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Phone Number *"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              error={errors.phone}
              placeholder="+91 4442116715"
            />
            <Input
              label="Subject *"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              error={errors.subject}
              placeholder="Brief subject of your inquiry"
            />
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Message *
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              rows={6}
              className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none ${
                errors.message ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Please provide details about your inquiry..."
            />
            {errors.message && (
              <p className="text-sm text-red-600 mt-1">{errors.message}</p>
            )}
          </div>

          {/* reCAPTCHA */}
          <div>
            <ReCaptcha
              ref={captchaRef}
              onVerify={handleCaptchaVerify}
              onError={handleCaptchaError}
              onExpired={handleCaptchaExpired}
              size="normal"
              theme="light"
            />
            {captchaError && (
              <p className="text-sm text-red-600 mt-1">{captchaError}</p>
            )}
            {errors.captcha && (
              <p className="text-sm text-red-600 mt-1">{errors.captcha}</p>
            )}
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            variant="gradient"
            size="lg"
            className="w-full"
            disabled={isSubmitting || isVerifying}
            glow
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Sending Message...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                <FaPaperPlane />
                <span>Send Message</span>
              </div>
            )}
          </Button>

          {/* Help Text */}
          {/* <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div className="flex items-start space-x-2">
              <FaLightbulb className="text-blue-500 text-lg" />
              <div className="text-sm text-blue-700">
                <p className="font-semibold mb-1">Need immediate assistance?</p>
                <p>For urgent matters, please call us directly at <strong>+91 4442116715</strong> or email <strong><EMAIL></strong></p>
              </div>
            </div>
          </div> */}
        </form>
      </CardContent>
    </Card>
  );
};

export default ContactForm;
