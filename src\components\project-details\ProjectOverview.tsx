import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui';
import { Project } from '../../hooks';

interface ProjectOverviewProps {
  project: Project;
}

const ProjectOverview: React.FC<ProjectOverviewProps> = ({ project }) => {
  return (
    <Card gradient>
      <CardHeader>
        <h2 className="text-xl lg:text-2xl font-bold text-gray-900">Project Overview</h2>
      </CardHeader>
      <CardContent>
        <p className="text-gray-700 leading-relaxed text-base lg:text-lg">
          {project.description}
        </p>
      </CardContent>
    </Card>
  );
};

export default ProjectOverview;
