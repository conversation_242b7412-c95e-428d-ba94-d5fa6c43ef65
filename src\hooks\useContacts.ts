import { useState, useEffect } from 'react';
import contactService, { Contact, ContactsResponse, UpdateContactRequest, AddNoteRequest, ContactRequest } from '../services/contactService';
import { useWebSocketField } from '../contexts/WebSocketContext';

interface ContactFilters {
  text?: string;
  status?: string;
  source?: string;
  priority?: string;
  enquiryType?: string;
  startDate?: string;
  endDate?: string;
}

interface UseContactsReturn {
  contacts: Contact[];
  stats: {
    totalCount: number;
    newCount: number;
    inProgressCount: number;
    resolvingCount: number;
    closedCount: number;
  };
  loading: boolean;
  error: string | null;
  filters: ContactFilters;
  refreshContacts: () => Promise<void>;
  createContact: (data: Omit<ContactRequest, 'status' | 'priority' | 'source'>) => Promise<void>;
  updateContact: (id: number, data: UpdateContactRequest) => Promise<void>;
  deleteContact: (id: number) => Promise<void>;
  addNote: (id: number, notes: AddNoteRequest[]) => Promise<void>;
  deleteNote: (contactId: number, noteId: number) => Promise<void>;
  updateFilters: (newFilters: Partial<ContactFilters>) => void;
  applyFilters: () => Promise<void>;
  resetFilters: () => Promise<void>;
}

const useContacts = (): UseContactsReturn => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [stats, setStats] = useState({
    totalCount: 0,
    newCount: 0,
    inProgressCount: 0,
    resolvingCount: 0,
    closedCount: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ContactFilters>({
    text: '',
    status: 'ALL',
    source: 'ALL',
    priority: 'ALL',
    enquiryType: 'ALL',
    startDate: '',
    endDate: ''
  });

  const refreshContacts = async () => {
    try {
      setLoading(true);
      setError(null);

      const response: ContactsResponse = await contactService.getContacts(filters);
      
      setContacts(response.data.contacts);
      setStats(response.data.contactTypeList);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch contacts');
      console.error('Error fetching contacts:', err);
    } finally {
      setLoading(false);
    }
  };

  const createContact = async (data: Omit<ContactRequest, 'status' | 'priority' | 'source'>) => {
    try {
      await contactService.createContact(data);
      await refreshContacts(); // Refresh the list after creation
    } catch (err: any) {
      setError(err.message || 'Failed to create contact');
      throw err;
    }
  };

  const updateContact = async (id: number, data: UpdateContactRequest) => {
    try {
      await contactService.updateContact(id, data);
      await refreshContacts(); // Refresh the list after update
    } catch (err: any) {
      setError(err.message || 'Failed to update contact');
      throw err;
    }
  };

  const deleteContact = async (id: number) => {
    try {
      await contactService.deleteContact(id);
      await refreshContacts(); // Refresh the list after deletion
    } catch (err: any) {
      setError(err.message || 'Failed to delete contact');
      throw err;
    }
  };

  const addNote = async (id: number, notes: AddNoteRequest[]) => {
    try {
      await contactService.addNote(id, notes);
      await refreshContacts(); // Refresh the list after adding note
    } catch (err: any) {
      setError(err.message || 'Failed to add note');
      throw err;
    }
  };

  const deleteNote = async (contactId: number, noteId: number) => {
    try {
      await contactService.deleteContactNote(contactId, noteId);
      await refreshContacts(); // Refresh the list after deleting note
    } catch (err: any) {
      setError(err.message || 'Failed to delete note');
      throw err;
    }
  };

  // Load contacts on mount
  // Filter functions
  const updateFilters = (newFilters: Partial<ContactFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const applyFilters = async () => {
    await refreshContacts();
  };

  const resetFilters = async () => {
    // Reset filters to default state with proper date format
    setFilters({
      text: '',
      status: 'ALL',
      source: 'ALL',
      priority: 'ALL',
      enquiryType: 'ALL',
      startDate: '',
      endDate: ''
    });
    // Immediately fetch contacts without any filters
    try {
      setLoading(true);
      const response: ContactsResponse = await contactService.getContacts();

      setContacts(response.data.contacts);
      setStats(response.data.contactTypeList);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load contacts');
      console.error('Error fetching contacts:', err);
    } finally {
      setLoading(false);
    }
  };

  // WebSocket integration for real-time updates
  const contactMessage = useWebSocketField('CONTACT');

  useEffect(() => {
    if (contactMessage) {
      console.log('📩 Contact WebSocket update:', contactMessage);

      switch (contactMessage.event) {
        case 'ADD':
          // Refresh contacts to get the new contact
          refreshContacts();
          break;
        case 'UPDATE':
          // Update specific contact or refresh all
          if (contactMessage.data?.contacts) {
            setContacts(contactMessage.data.contacts);
            if (contactMessage.data.contactTypeList) {
              setStats(contactMessage.data.contactTypeList);
            }
          } else {
            refreshContacts();
          }
          break;
        case 'DELETE':
          // Refresh contacts to remove deleted contact
          refreshContacts();
          break;
        default:
          console.log('Unknown contact event:', contactMessage.event);
      }
    }
  }, [contactMessage]);

  useEffect(() => {
    refreshContacts();
  }, []);

  return {
    contacts,
    stats,
    loading,
    error,
    filters,
    refreshContacts,
    createContact,
    updateContact,
    deleteContact,
    addNote,
    deleteNote,
    updateFilters,
    applyFilters,
    resetFilters,
  };
};

export default useContacts;
