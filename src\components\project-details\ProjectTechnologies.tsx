import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON>, Badge } from '../ui';
import { FaTools } from 'react-icons/fa';
import { Project } from '../../hooks';

interface ProjectTechnologiesProps {
  project: Project;
}

const ProjectTechnologies: React.FC<ProjectTechnologiesProps> = ({ project }) => {
  return (
    <Card gradient>
      <CardHeader>
        <h2 className="text-xl lg:text-2xl font-bold text-gray-900 flex items-center">
          <FaTools className="mr-2 lg:mr-3 text-lg lg:text-xl text-gray-600" />
          Technologies & Tools
        </h2>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2 lg:gap-3">
          {project.technologies.map((tech, index) => (
            <Badge 
              key={index} 
              variant="purple" 
              size="md" 
              className="hover:scale-110 transition-transform text-xs lg:text-sm"
            >
              {tech}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProjectTechnologies;
