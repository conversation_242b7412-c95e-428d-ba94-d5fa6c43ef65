import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Footer } from '../layout';
import { Button } from '../ui';

const ApplicationNotFoundState: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
      <Header />
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Project Not Found</h1>
          <p className="text-gray-600 mb-6">The project you're trying to apply for doesn't exist.</p>
          <Button onClick={() => navigate('/')}>Back to Home</Button>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ApplicationNotFoundState;
