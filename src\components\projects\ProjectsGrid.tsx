import React from 'react';
import { Project } from '../../hooks';
import ProjectCard from '../home/<USER>';

interface ProjectsGridProps {
  projects: Project[];
  onViewDetails: (projectId: string) => void;
}

const ProjectsGrid: React.FC<ProjectsGridProps> = ({ projects, onViewDetails }) => {
  return (
    <div 
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
      role="grid"
      aria-label="Projects grid"
    >
      {projects.map(project => (
        <div key={project.id} role="gridcell">
          <ProjectCard
            project={project}
            onViewDetails={onViewDetails}
          />
        </div>
      ))}
    </div>
  );
};

export default ProjectsGrid;
