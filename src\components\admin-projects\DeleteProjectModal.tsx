import React, { useState } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from '../ui';
import { FaExclamationTriangle } from 'react-icons/fa';
import { Project } from '../../types/projects';

interface DeleteProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (projectId: string) => Promise<{ success: boolean; error?: string }>;
  project: Project | null;
}

const DeleteProjectModal: React.FC<DeleteProjectModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  project
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleConfirm = async () => {
    if (!project) return;

    setIsLoading(true);
    setError('');

    const result = await onConfirm(project.id);

    if (result.success) {
      onClose();
    } else {
      setError(result.error || 'Failed to delete project');
    }

    setIsLoading(false);
  };

  const handleClose = () => {
    if (!isLoading) {
      setError('');
      onClose();
    }
  };


  if (!project) return null;

  return (
    <Modal title='Delete Project' isOpen={isOpen} onClose={handleClose} size="md">
      <div className="p-6">
        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Project Info */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-start space-x-3">
            {project.image ? (
              <img
                src={project.image}
                alt={project.title}
                className="w-16 h-16 object-cover rounded-lg flex-shrink-0"
              />
            ) : (
              <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center rounded-lg flex-shrink-0">
                <div className="text-center text-gray-500">
                  <div className="text-sm">📋</div>
                </div>
              </div>
            )}
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">{project.title}</h3>
              <p className="text-sm text-gray-600 mb-2">{project.category}</p>
              <p className="text-sm text-gray-700 line-clamp-2">{project.description}</p>
              <div className="flex items-center space-x-2 mt-2">
                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  {project.status}
                </span>
                <span className="text-xs text-gray-500">
                  {project.applicationCount}/{project.maxInterns} applications
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Warning Message */}
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <FaExclamationTriangle className="text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800 mb-1">Warning</h4>
              <p className="text-sm text-yellow-700">
                Deleting this project will permanently remove:
              </p>
              <ul className="text-sm text-yellow-700 mt-2 space-y-1">
                <li>• Project information and configuration</li>
                <li>• {project.questionCount} prerequisite questions</li>
                <li>• All application data and submissions</li>
                <li>• Team member assignments</li>
                <li>• Project statistics and history</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Impact Information */}
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">Impact Assessment</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-blue-700">Current Applications</p>
              <p className="font-semibold text-blue-900">{project.applicationCount}</p>
            </div>
            <div>
              <p className="text-blue-700">Prerequisites</p>
              <p className="font-semibold text-blue-900">{project.questionCount} questions</p>
            </div>
            <div>
              <p className="text-blue-700">Technologies</p>
              <p className="font-semibold text-blue-900">{project.technologies.length} listed</p>
            </div>
          </div>
        </div>

        {/* Confirmation Text */}
        <div className="mb-6">
          <p className="text-gray-700">
            Are you sure you want to delete <strong>"{project.title}"</strong>? This action cannot be undone and will affect all associated data.
          </p>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            onClick={handleClose}
            variant="outline"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            variant="primary"
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Deleting...</span>
              </div>
            ) : (
              'Delete Project'
            )}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteProjectModal;
