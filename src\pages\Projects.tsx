import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Footer } from '../components/layout';
import { useProjects, useProjectsFilters } from '../hooks';
import {
  ProjectsHero,
  ProjectsFilters,
  ProjectsGrid,
  ProjectsEmptyState,
  ProjectsCallToAction,
  ProjectsLoadingState,
  ProjectsErrorState
} from '../components/projects';

const Projects: React.FC = () => {
  const navigate = useNavigate();
  const { projects, loading, error } = useProjects();

  // Use the custom hook for filter management
  const {
    searchTerm,
    setSearchTerm,
    filteredProjects,
    clearFilters
  } = useProjectsFilters(projects);

  const handleViewDetails = (projectId: string) => {
    navigate(`/project/${projectId}`);
  };

  // Loading state
  if (loading) {
    return <ProjectsLoadingState />;
  }

  // Error state
  if (error) {
    return <ProjectsErrorState error={error} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
      <Header />

      {/* Hero Section */}
      <ProjectsHero projects={projects} />

      {/* Main Content */}
      <main id="main-content" className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12" role="main" aria-label="Projects listing">
        {/* Enhanced Filters */}
        <ProjectsFilters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filteredCount={filteredProjects.length}
          totalCount={projects.length}
          onClearFilters={clearFilters}
        />

        {/* Projects Grid or Empty State */}
        {filteredProjects.length === 0 ? (
          <ProjectsEmptyState onShowAllProjects={clearFilters} />
        ) : (
          <ProjectsGrid
            projects={filteredProjects}
            onViewDetails={handleViewDetails}
          />
        )}

        {/* Call to Action */}
        <ProjectsCallToAction />
      </main>

      <Footer />
    </div>
  );
};

export default Projects;
