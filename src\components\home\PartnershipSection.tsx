import React from 'react';
import { FaGraduationCap, FaBriefcase, FaGlobe, FaAward, FaHandshake, FaStar } from 'react-icons/fa';
import ReactCountryFlag from 'react-country-flag';

// Import company logos
import inspeedaLogo from '../../assets/company/Inspeedia.png';
import mothercodeLogo from '../../assets/company/Mothercode.png';
import ziliconcloudLogo from '../../assets/company/Ziliconcloud.png';

interface Partner {
  id: number;
  name: string;
  country: string;
  countryCode: string;
  experience: string;
  logo: string;
  description: string;
  specialization: string;
}

const PartnershipSection: React.FC = () => {
  const partners: Partner[] = [
    {
      id: 1,
      name: "Mothercode Software Systems Pvt Ltd",
      country: "India",
      countryCode: "IN",
      experience: "25+ Years",
      logo: mothercodeLogo,
      description: "Premier software development company with expertise in enterprise solutions",
      specialization: "Enterprise Software"
    },
    {
      id: 2,
      name: "Ziliconcloud LLC",
      country: "USA",
      countryCode: "US",
      experience: "10+ Years",
      logo: ziliconcloudLogo,
      description: "Cloud-first technology company focused on modern scalable solutions",
      specialization: "Cloud Solutions"
    },
    {
      id: 3,
      name: "Inspeedia Inc",
      country: "Japan",
      countryCode: "JP",
      experience: "30+ Years",
      logo: inspeedaLogo,
      description: "Leading technology innovation company specializing in AI and automation solutions",
      specialization: "AI & Automation"
    },
  ];

  return (
    <section className="relative py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold mb-6">
            <FaHandshake className="mr-2" />
            Global Partnership Network
          </div>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
            Our International
            <span className="block bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-transparent bg-clip-text">
              Industry Partners
            </span>
          </h2>
          
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Collaborate with industry leaders from Japan, India, and USA. Get certified by our partners and 
            unlock direct hiring opportunities based on your internship performance.
          </p>
        </div>

        {/* Partners Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {partners.map((partner, index) => (
            <div
              key={partner.id}
              className="group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] border border-gray-200 hover:border-blue-300 flex flex-col min-h-[500px]"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Country Flag */}
              <div className="absolute -top-3 -right-3 w-12 h-12 bg-white rounded-full shadow-lg border-2 border-gray-100 flex items-center justify-center">
                <ReactCountryFlag
                  countryCode={partner.countryCode}
                  svg
                  style={{
                    width: '24px',
                    height: '18px',
                  }}
                  title={partner.country}
                />
              </div>

              {/* Company Logo */}
              <div className="flex justify-center mb-6">
                <div className="w-24 h-24 bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl p-4 flex items-center justify-center group-hover:scale-105 transition-transform duration-300 border border-slate-200">
                  <img
                    src={partner.logo}
                    alt={partner.name}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>

              {/* Company Info */}
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-slate-900 mb-3 group-hover:text-blue-700 transition-colors duration-300 min-h-[3.5rem] flex items-center justify-center">{partner.name}</h3>
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <FaGlobe className="text-blue-600" />
                  <span className="text-slate-600 font-medium">{partner.country}</span>
                </div>
                <div className="inline-flex items-center bg-blue-50 text-blue-700 px-4 py-2 rounded-full text-sm font-semibold border border-blue-200">
                  <FaStar className="mr-2" />
                  {partner.experience} Experience
                </div>
              </div>

              {/* Specialization */}
              <div className="text-center mb-6">
                <div className="bg-slate-50 rounded-xl p-4 border border-slate-200">
                  <p className="text-sm font-semibold text-slate-700 mb-2">Specialization</p>
                  <p className="text-lg font-bold text-slate-900">{partner.specialization}</p>
                </div>
              </div>

              {/* Description */}
              <div className="flex-grow flex items-center mt-4">
                <p className="text-slate-600 text-center leading-relaxed text-sm w-full">{partner.description}</p>
              </div>

              {/* Subtle Hover Effect */}
              <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-50 transition-opacity duration-300 -z-10"></div>
            </div>
          ))}
        </div>

        {/* Benefits Section */}
        <div className="bg-white rounded-3xl p-8 lg:p-12 shadow-xl border border-gray-100">
          <div className="text-center mb-12">
            <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
              Partnership Benefits & Opportunities
            </h3>
            <p className="text-lg text-gray-600">
              Exclusive advantages for high-performing students
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Certification Requirement */}
            <div className="text-center group">
              <div className="w-16 h-16 bg-emerald-100 border-2 border-emerald-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-105 group-hover:bg-emerald-200 transition-all duration-300">
                <FaGraduationCap className="text-emerald-700 text-2xl" />
              </div>
              <h4 className="text-lg font-bold text-slate-900 mb-2">Academic Excellence</h4>
              <p className="text-slate-600 text-sm">
                <span className="font-semibold text-emerald-700">7.5+ CGPA</span> required for get hired by our partners
              </p>
            </div>

            {/* Global Certification */}
            <div className="text-center group">
              <div className="w-16 h-16 bg-blue-100 border-2 border-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-105 group-hover:bg-blue-200 transition-all duration-300">
                <FaAward className="text-blue-700 text-2xl" />
              </div>
              <h4 className="text-lg font-bold text-slate-900 mb-2">Global Certification</h4>
              <p className="text-slate-600 text-sm">
                Receive <span className="font-semibold text-blue-700">internationally recognized</span> certificates from our partners
              </p>
            </div>

            {/* Performance Tracking */}
            <div className="text-center group">
              <div className="w-16 h-16 bg-purple-100 border-2 border-purple-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-105 group-hover:bg-purple-200 transition-all duration-300">
                <FaStar className="text-purple-700 text-2xl" />
              </div>
              <h4 className="text-lg font-bold text-slate-900 mb-2">Performance Based</h4>
              <p className="text-slate-600 text-sm">
                Your <span className="font-semibold text-purple-700">internship performance</span> determines hiring opportunities
              </p>
            </div>

            {/* Direct Hiring */}
            <div className="text-center group">
              <div className="w-16 h-16 bg-orange-100 border-2 border-orange-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-105 group-hover:bg-orange-200 transition-all duration-300">
                <FaBriefcase className="text-orange-700 text-2xl" />
              </div>
              <h4 className="text-lg font-bold text-slate-900 mb-2">Direct Hiring</h4>
              <p className="text-slate-600 text-sm">
                <span className="font-semibold text-orange-700">Direct placement</span> opportunities with partner companies
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-12">
            <div className="bg-slate-50 rounded-2xl p-8 border border-slate-200">
              <h4 className="text-xl font-bold text-slate-900 mb-4">Ready to Join Our Global Network?</h4>
              <p className="text-slate-600 mb-6">
                Start your journey with MSS Internship and unlock opportunities with our international partners
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                <div className="flex items-center text-sm text-slate-700 bg-white px-4 py-2 rounded-lg border border-slate-200">
                  <FaGraduationCap className="mr-2 text-emerald-600" />
                  <span>Maintain 7.5+ CGPA</span>
                </div>
                <div className="flex items-center text-sm text-slate-700 bg-white px-4 py-2 rounded-lg border border-slate-200">
                  <FaStar className="mr-2 text-purple-600" />
                  <span>Excel in Internship</span>
                </div>
                <div className="flex items-center text-sm text-slate-700 bg-white px-4 py-2 rounded-lg border border-slate-200">
                  <FaBriefcase className="mr-2 text-orange-600" />
                  <span>Get Hired Globally</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PartnershipSection;
