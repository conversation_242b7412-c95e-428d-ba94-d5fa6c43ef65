import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader } from '../ui';
import applicationService from '../../services/applicationService';

interface OTPVerificationStepProps {
  email: string;
  projectId: string;
  onComplete: (stepData?: any) => void;
  onBack: () => void;
}

const OTPVerificationStep: React.FC<OTPVerificationStepProps> = ({ email, projectId, onComplete, onBack }) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const [canResend, setCanResend] = useState(false);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    // Focus first input on mount
    inputRefs.current[0]?.focus();
  }, []);

  useEffect(() => {
    // Countdown timer
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleInputChange = (index: number, value: string) => {
    if (value.length > 1) return; // Only allow single digit
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError('');

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-verify when all digits are entered
    if (newOtp.every(digit => digit !== '') && newOtp.join('').length === 6) {
      setTimeout(() => verifyOTP(newOtp.join('')), 100);
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      // Focus previous input on backspace
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6);
    
    if (pastedData.length === 6) {
      const newOtp = pastedData.split('');
      setOtp(newOtp);
      setError('');
      setTimeout(() => verifyOTP(pastedData), 100);
    }
  };

  const verifyOTP = async (otpValue: string) => {
    setIsVerifying(true);
    setError('');

    try {
      // Use real API to verify OTP
      await applicationService.verifyOTP(email, otpValue);

      // If successful, proceed to next step
      onComplete();
    } catch (error: any) {
      setError(error.message || 'Invalid OTP. Please try again.');
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } finally {
      setIsVerifying(false);
    }
  };

  const handleManualVerify = () => {
    const otpValue = otp.join('');
    if (otpValue.length === 6) {
      verifyOTP(otpValue);
    } else {
      setError('Please enter the complete 6-digit OTP');
    }
  };

  const handleResendOTP = async () => {
    try {
      setError('');

      // Resend OTP using real API
      await applicationService.sendOTP(email, projectId);

      // Reset timer and state
      setTimeLeft(300);
      setCanResend(false);
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();

      // Show success message briefly
      setError('OTP sent successfully!');
      setTimeout(() => setError(''), 3000);
    } catch (error: any) {
      setError(error.message || 'Failed to resend OTP. Please try again.');
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <Card gradient>
        <CardHeader>
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl">🔐</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Verify Your Email</h2>
            <p className="text-gray-600">
              We've sent a 6-digit verification code to
            </p>
            <p className="font-semibold text-purple-600">{email}</p>
          </div>
        </CardHeader>

        <CardContent>
          <div className="space-y-6">
            {/* OTP Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3 text-center">
                Enter Verification Code
              </label>
              <div className="flex justify-center space-x-2" onPaste={handlePaste}>
                {otp.map((digit, index) => (
                  <input
                    key={index}
                    ref={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]"
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleInputChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    className={`w-12 h-12 text-center text-xl font-bold border-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200 ${
                      error 
                        ? 'border-red-500 bg-red-50' 
                        : digit 
                        ? 'border-purple-500 bg-purple-50' 
                        : 'border-gray-300'
                    }`}
                    disabled={isVerifying}
                  />
                ))}
              </div>
              {error && (
                <p className="text-sm text-red-600 text-center mt-2">{error}</p>
              )}
            </div>

            {/* Timer */}
            <div className="text-center">
              {timeLeft > 0 ? (
                <p className="text-sm text-gray-600">
                  Code expires in <span className="font-semibold text-purple-600">{formatTime(timeLeft)}</span>
                </p>
              ) : (
                <p className="text-sm text-red-600">
                  Code has expired. Please request a new one.
                </p>
              )}
            </div>

            {/* Verify Button */}
            <Button
              onClick={handleManualVerify}
              disabled={otp.join('').length !== 6 || isVerifying}
              variant="gradient"
              className="w-full"
              glow
            >
              {isVerifying ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Verifying...</span>
                </div>
              ) : (
                'Verify Code'
              )}
            </Button>

            {/* Resend OTP */}
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-2">Didn't receive the code?</p>
              <Button
                onClick={handleResendOTP}
                disabled={!canResend}
                variant="ghost"
                className="text-purple-600 hover:text-purple-700"
              >
                {canResend ? 'Resend Code' : `Resend in ${formatTime(timeLeft)}`}
              </Button>
            </div>

            {/* Help Text */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 text-lg">💡</span>
                <div className="text-sm text-blue-700">
                  <p className="font-semibold mb-1">For testing purposes:</p>
                  <p>Use OTP: <span className="font-mono bg-blue-100 px-2 py-1 rounded">000000</span></p>
                </div>
              </div>
            </div>

            {/* Back Button */}
            <Button
              onClick={onBack}
              variant="outline"
              className="w-full"
              disabled={isVerifying}
            >
              Back to Details
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OTPVerificationStep;
