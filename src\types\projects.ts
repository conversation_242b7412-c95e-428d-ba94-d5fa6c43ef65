export interface PrerequisiteQuestion {
  id?: string;
  text: string;
  questionType: 'BOOLEAN' | 'SELECT' | 'MULTISELECT' | 'TEXT';
  options: string[];
  required: boolean;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  technologies: string[];
  duration: string;
  category: string;
  type?: string;
  requirements: string[];
  gains: string[];
  maxInterns: number;
  applicationCount?: number;
  status: 'ACTIVE' | 'INACTIVE' | 'CLOSED' ;
  prerequisiteQuestions: PrerequisiteQuestion[];
  questionCount?: number;
}

export interface ProjectsResponse {
  projectDetailList: Project[];
  projectmanagementCount: {
    totalProjectCount: number;
    activeProjectCount: number;
    inactiveProjectCount: number;
    closedProjectCount: number;
  };
}

export interface ProjectFormData {
  title: string;
  description: string;
  technologies: string[];
  duration: string;
  category: string;
  requirements: string[];
  gains: string[];
  maxInterns: number;
  status: 'ACTIVE' | 'INACTIVE' | 'CLOSED';
  prerequisiteQuestions: PrerequisiteQuestion[];
  image?: File | null;
  deleteImage: boolean;
}

export interface ProjectDetailResponse {
  detailedProjectInfo: {
    title: string;
    type: string;
    description: string;
    technologies: string[];
    image: string;
    status: 'ACTIVE' | 'INACTIVE' | 'CLOSED';
    requirements: string[];
    gains: string[];
    duration: string;
    questionCount: number;
    applicationCount: number;
    maxInterns: number;
  };
  prerequisiteQuestions: PrerequisiteQuestion[];
}
