import React from 'react';
import { Card, CardContent } from '../ui';
import { <PERSON>a<PERSON><PERSON><PERSON>, Fa<PERSON>ser<PERSON><PERSON>, FaUserCog, FaCheckCircle, FaPauseCircle } from 'react-icons/fa';

interface TeamsStatsProps {
  stats: {
    total: number;
    active: number;
    inactive: number;
    totalMembers: number;
    managers: number;
    teamLeaders: number;
  };
}

const TeamsStats: React.FC<TeamsStatsProps> = ({ stats }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Total Teams */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-blue-100 rounded-full">
              <FaUsers className="text-blue-600 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Total Teams</p>
              <h3 className="text-2xl font-bold text-gray-900">{stats.total}</h3>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Active Teams */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-green-100 rounded-full">
              <FaCheckCircle className="text-green-600 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Active Teams</p>
              <h3 className="text-2xl font-bold text-gray-900">{stats.active}</h3>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inactive Teams */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gray-100 rounded-full">
              <FaPauseCircle className="text-gray-600 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Inactive Teams</p>
              <h3 className="text-2xl font-bold text-gray-900">{stats.inactive}</h3>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Total Members */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-purple-100 rounded-full">
              <FaUsers className="text-purple-600 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Total Interns</p>
              <h3 className="text-2xl font-bold text-gray-900">{stats.totalMembers}</h3>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Managers */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-indigo-100 rounded-full">
              <FaUserTie className="text-indigo-600 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Managers</p>
              <h3 className="text-2xl font-bold text-gray-900">{stats.managers}</h3>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Leaders */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-cyan-100 rounded-full">
              <FaUserCog className="text-cyan-600 text-xl" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Team Leaders</p>
              <h3 className="text-2xl font-bold text-gray-900">{stats.teamLeaders}</h3>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TeamsStats;
