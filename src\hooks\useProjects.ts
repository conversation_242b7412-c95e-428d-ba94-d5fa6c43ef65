import { useState, useEffect, useCallback, useContext } from 'react';
import projectService from '../services/projectService';
import { Project } from '../types/projects'; // Assuming you have these types defined
import PublicWebSocketContext, { PublicWebSocketMessage } from '../contexts/PublicWebSocketContext';

export const useProjects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [project, setProject] = useState<Project>({} as Project);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [projectLoading, setProjectLoading] = useState(false);
  const [projectError, setProjectError] = useState<string | null>(null);

  // WebSocket integration for real-time project updates (safe version)
  const webSocketContext = useContext(PublicWebSocketContext);

  // Create a safe WebSocket field hook that returns null if context is not available
  const useSafePublicWebSocketField = (field: 'PROJECT') => {
    const [fieldMessage, setFieldMessage] = useState<PublicWebSocketMessage | null>(null);

    useEffect(() => {
      if (!webSocketContext) {
        return; // No context available, skip WebSocket functionality
      }

      try {
        const unsubscribe = webSocketContext.subscribe((message: PublicWebSocketMessage) => {
          if (message.field === field) {
            setFieldMessage(message);
          }
        });

        return unsubscribe;
      } catch (error) {
        console.log('WebSocket not available, using standard API calls only');
        return;
      }
    }, [field, webSocketContext]);

    return fieldMessage;
  };

  const projectMessage = useSafePublicWebSocketField('PROJECT');

  useEffect(() => {
    if (projectMessage && webSocketContext) {
      console.log('📩 Public Project WebSocket update:', projectMessage);

      switch (projectMessage.event) {
        case 'ADD':
          // New project added - update projects list
          if (projectMessage.data && Array.isArray(projectMessage.data)) {
            setProjects(projectMessage.data);
          } else {
            fetchProjects(); // Fallback to full refresh
          }
          break;
        case 'UPDATE':
          // Project updated - update projects list
          if (projectMessage.data && Array.isArray(projectMessage.data)) {
            setProjects(projectMessage.data);
          } else {
            fetchProjects(); // Fallback to full refresh
          }
          break;
        case 'DELETE':
          // Project deleted - refresh projects list
          if (projectMessage.data && Array.isArray(projectMessage.data)) {
            setProjects(projectMessage.data);
          } else {
            fetchProjects(); // Fallback to full refresh
          }
          break;
        default:
          console.log('Unknown project event:', projectMessage.event);
      }
    }
  }, [projectMessage, webSocketContext]);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const { projects: fetchedProjects } = await projectService.fetchProjects();
      setProjects(fetchedProjects);
      setError(null);
    } catch (err) {
      setError('Failed to load projects');
      console.error('Error fetching projects:', err);
    } finally {
      setLoading(false);
    }
  };

  const getProjectById = useCallback(async (id: string): Promise<void> => {
    try {
      setProjectLoading(true);
      setProjectError(null);
      const project = await projectService.getProjectById(id);
      setProject(project);
    } catch (err) {
      setProjectError('Failed to load project details');
      console.error('Error fetching project:', err);
    } finally {
      setProjectLoading(false);
    }
  }, []);

  const getProjectsByCategory = (category: string): Project[] => {
    return projectService.filterProjectsByCategory(projects, category);
  };

  const getOpenProjects = (): Project[] => {
    return projectService.getOpenProjects(projects);
  };

  return {
    projects,
    project,
    loading,
    projectLoading,
    error,
    projectError,
    getProjectById,
    getProjectsByCategory,
    getOpenProjects,
    refreshProjects: fetchProjects
  };
};