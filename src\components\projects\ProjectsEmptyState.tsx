import React from 'react';
import { Button } from '../ui';
import { FaSearch } from 'react-icons/fa';

interface ProjectsEmptyStateProps {
  onShowAllProjects: () => void;
}

const ProjectsEmptyState: React.FC<ProjectsEmptyStateProps> = ({ onShowAllProjects }) => {
  return (
    <div className="flex items-center justify-center py-20">
      <div className="text-center w-full max-w-lg">
        <div className="flex justify-center mb-8">
          <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center">
            <FaSearch className="text-gray-400 text-3xl" />
          </div>
        </div>
        <div className="space-y-6">
          <h3 className="text-3xl md:text-4xl font-bold text-gray-900">No Projects Found</h3>
          <p className="text-lg text-gray-600 leading-relaxed max-w-md mx-auto">
            Try adjusting your search criteria or filters to find more projects that match your interests.
          </p>
          <div className="pt-4">
            <Button
              onClick={onShowAllProjects}
              variant="gradient"
              size="lg"
            >
              Show All Projects
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectsEmptyState;
