import React from 'react';
import { Card, CardContent, Button } from '../ui';
import { FaEye } from 'react-icons/fa';
import { InternApplicant } from '../../services/internService';
import internService from '../../services/internService';

interface ApplicationCardProps {
  application: InternApplicant;
  onView: (application: InternApplicant) => void;
}

const ApplicationCard: React.FC<ApplicationCardProps> = ({ application, onView }) => {
  const getStatusColor = (status: InternApplicant['applicationStatus']) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'REVIEWING':
        return 'bg-blue-100 text-blue-800';
      case 'ACCEPTED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Student Info */}
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                {application.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="text-lg font-semibold text-gray-900">{application.name}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.applicationStatus)}`}>
                    {internService.getApplicationStatusDisplayName(application.applicationStatus)}
                  </span>
                </div>
                <p className="text-gray-600 mb-1">Application ID: {application.id}</p>
                <p className="text-gray-600 mb-1">{application.email}</p>
                <p className="text-gray-600 text-sm">{application.phone}</p>
              </div>
            </div>

            {/* Project and Application Info */}
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-700">Project</p>
                <p className="text-gray-900">{application.projectName}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Applied</p>
                <p className="text-gray-900">{formatDate(application.appliedAt)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Prerequisites Answered</p>
                <p className="text-gray-900">{application.answerCount} questions</p>
              </div>
            </div>

            {/* Technologies Preview */}
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Technologies</p>
              <div className="flex flex-wrap gap-1">
                {application.technologies.slice(0, 5).map((tech, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                  >
                    {tech}
                  </span>
                ))}
                {application.technologies.length > 5 && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                    +{application.technologies.length - 5} more
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="ml-4">
            <Button
              onClick={() => onView(application)}
              variant="outline"
              size="sm"
              className="flex items-center space-x-1"
            >
              <FaEye />
              <span>View Details</span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ApplicationCard;
