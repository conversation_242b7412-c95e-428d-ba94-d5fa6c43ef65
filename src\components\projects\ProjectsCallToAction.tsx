import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, Button } from '../ui';
import { FaPhone, FaHome, FaRocket } from 'react-icons/fa';

const ProjectsCallToAction: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="mt-20">
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
        <CardContent className="p-12 text-center">
          <div className="w-20 h-20 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center mx-auto mb-6">
            <FaRocket className="text-white text-3xl" />
          </div>
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Ready to Start Your Journey?</h3>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            Don't see the perfect project yet? New opportunities are added regularly. 
            Get in touch with us to discuss custom project options.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Button
              onClick={() => navigate('/contact')}
              variant="gradient"
              size="lg"
              glow
              className="flex items-center space-x-2"
            >
              <FaPhone />
              <span>Contact Us</span>
            </Button>
            <Button
              onClick={() => navigate('/')}
              variant="outline"
              size="lg"
              className="flex items-center space-x-2"
            >
              <FaHome />
              <span>Back to Home</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectsCallToAction;
