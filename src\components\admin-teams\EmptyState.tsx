import React from 'react';
import { But<PERSON> } from '../ui';
import { FaUsers, FaPlus } from 'react-icons/fa';

interface EmptyStateProps {
  onAddTeam: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({ onAddTeam }) => {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <FaUsers className="text-4xl text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">No teams found</h3>
      <p className="text-gray-500 mb-6">Get started by creating your first team.</p>
      <Button onClick={onAddTeam} className="bg-blue-600 hover:bg-blue-700">
        <FaPlus className="mr-2" />
        Add Team
      </Button>
    </div>
  );
};

export default EmptyState;
