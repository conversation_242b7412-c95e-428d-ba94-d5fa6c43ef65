import React, { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { Header, Footer } from '../components/layout';
import { trackingService, ContactTrackingData, InternshipTrackingData } from '../services/trackingService';
import { FaSearch, FaSpinner, FaCheckCircle, FaClock, FaExclamationTriangle, FaTimesCircle, FaEnvelope, FaPhone, FaCalendarAlt, FaUser, FaProjectDiagram } from 'react-icons/fa';

const TrackApplication: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const navigate = useNavigate();

  // Determine tracking type based on URL path
  const getTrackingTypeFromPath = (): 'contact' | 'internship' => {
    if (location.pathname.includes('/track/contact-inquiry/')) {
      return 'contact';
    } else if (location.pathname.includes('/track/internship-application/')) {
      return 'internship';
    }
    return 'internship'; // default
  };

  const [trackingId, setTrackingId] = useState(id || '');
  const [trackingType, setTrackingType] = useState<'contact' | 'internship'>(getTrackingTypeFromPath());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [contactData, setContactData] = useState<ContactTrackingData | null>(null);
  const [internshipData, setInternshipData] = useState<InternshipTrackingData | null>(null);



  useEffect(() => {
    // Update tracking type when path changes
    setTrackingType(getTrackingTypeFromPath());

    if (id) {
      handleTrack();
    }
  }, [id, location.pathname]);

  const handleTrack = async () => {
    if (!trackingId.trim()) {
      setError('Please enter a tracking ID');
      return;
    }

    // If we're on the main track page, navigate to the specific tracking URL
    if (location.pathname === '/track') {
      const targetPath = trackingType === 'contact'
        ? `/track/contact-inquiry/${trackingId}`
        : `/track/internship-application/${trackingId}`;
      navigate(targetPath);
      return;
    }

    // If we're already on a specific tracking page, fetch the data
    setLoading(true);
    setError(null);
    setContactData(null);
    setInternshipData(null);

    try {
      if (trackingType === 'contact') {
        const response = await trackingService.trackContact(trackingId);
        setContactData(response.data);
      } else {
        const response = await trackingService.trackInternshipApplication(trackingId);
        setInternshipData(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while tracking');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toUpperCase()) {
      case 'NEW':
      case 'PENDING':
        return <FaClock className="w-5 h-5" />;
      case 'INPROGRESS':
      case 'REVIEWING':
        return <FaSpinner className="w-5 h-5 animate-spin" />;
      case 'RESOLVED':
      case 'ACCEPTED':
        return <FaCheckCircle className="w-5 h-5" />;
      case 'CLOSED':
      case 'REJECTED':
        return <FaTimesCircle className="w-5 h-5" />;
      default:
        return <FaExclamationTriangle className="w-5 h-5" />;
    }
  };



  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-16 sm:py-20 md:py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {location.pathname === '/track'
                ? 'Track Your Application'
                : trackingType === 'contact'
                  ? 'Contact Inquiry Status'
                  : 'Internship Application Status'
              }
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {location.pathname === '/track'
                ? 'Enter your tracking ID to check the status of your application or contact inquiry'
                : id
                  ? `Tracking details for ID: ${id}`
                  : 'Enter your tracking ID to view the status'
              }
            </p>
          </div>

          {/* Search Form */}
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 mb-8 backdrop-blur-sm">
            <form onSubmit={(e) => { e.preventDefault(); handleTrack(); }} className="space-y-8">
              {/* Type Selection - Only show on main track page */}
              {location.pathname === '/track' && (
                <div className="flex justify-center">
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-2 flex shadow-inner border border-gray-200">
                    <button
                      type="button"
                      onClick={() => setTrackingType('internship')}
                      className={`px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform ${
                        trackingType === 'internship'
                          ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg scale-105'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-white hover:shadow-md'
                      }`}
                    >
                      <span className="flex items-center">
                        <FaUser className="mr-2" />
                        Internship Application
                      </span>
                    </button>
                    <button
                      type="button"
                      onClick={() => setTrackingType('contact')}
                      className={`px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform ${
                        trackingType === 'contact'
                          ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg scale-105'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-white hover:shadow-md'
                      }`}
                    >
                      <span className="flex items-center">
                        <FaEnvelope className="mr-2" />
                        Contact Inquiry
                      </span>
                    </button>
                  </div>
                </div>
              )}

              {/* Input Field with Modern Design */}
              <div className="max-w-lg mx-auto">
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-2xl blur opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
                  <div className="relative">
                    <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg" />
                    <input
                      type="text"
                      value={trackingId}
                      onChange={(e) => setTrackingId(e.target.value)}
                      placeholder={id ? `Tracking ID: ${id}` : "Enter your tracking ID (e.g., 1234567890)"}
                      className="w-full pl-12 pr-4 py-4 bg-white border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 text-lg font-medium placeholder-gray-400 shadow-sm hover:shadow-md disabled:bg-gray-50 disabled:cursor-not-allowed"
                      disabled={!!id}
                      autoComplete="off"
                    />
                    {trackingId && !id && (
                      <button
                        type="button"
                        onClick={() => setTrackingId('')}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                      >
                        <FaTimesCircle className="text-lg" />
                      </button>
                    )}
                  </div>
                </div>

                {/* Helper Text */}
                <p className="text-center text-sm text-gray-500 mt-3">
                  {location.pathname === '/track'
                    ? 'Press Enter or click the button below to track your application'
                    : id
                      ? 'Your tracking information will appear below'
                      : 'Enter your tracking ID and press Enter to view status'
                  }
                </p>
              </div>

              {/* Search Button with Modern Design */}
              <div className="text-center">
                <button
                  type="submit"
                  disabled={loading || (!!id && !!(contactData || internshipData)) || !trackingId.trim()}
                  className="group relative inline-flex items-center px-10 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold rounded-2xl hover:from-blue-700 hover:to-indigo-700 focus:ring-4 focus:ring-blue-500/30 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-2xl blur opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                  <div className="relative flex items-center">
                    {loading ? (
                      <>
                        <FaSpinner className="animate-spin mr-3 text-lg" />
                        <span className="text-lg">Loading...</span>
                      </>
                    ) : id && (contactData || internshipData) ? (
                      <>
                        <FaCheckCircle className="mr-3 text-lg" />
                        <span className="text-lg">Tracking Complete</span>
                      </>
                    ) : (
                      <>
                        <FaSearch className="mr-3 text-lg" />
                        <span className="text-lg">
                          {location.pathname === '/track' ? 'Track Application' : 'Refresh Status'}
                        </span>
                      </>
                    )}
                  </div>
                </button>
              </div>
            </form>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
              <div className="flex items-center">
                <FaExclamationTriangle className="text-red-500 mr-3" />
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Results */}
          {(contactData || internshipData) && (
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">
                      {trackingType === 'contact' ? 'Contact Inquiry Status' : 'Internship Application Status'}
                    </h2>
                    <p className="text-blue-100">
                      {trackingType === 'contact' ? 'Your inquiry details and current status' : 'Your application progress and details'}
                    </p>
                  </div>


                </div>
              </div>

              <div className="p-8">
                {/* Contact Data */}
                {contactData && (
                  <div className="space-y-6">
                    {/* Status and Priority */}
                    <div className="flex flex-wrap gap-4 mb-6">
                      <div className="flex items-center">
                        {getStatusIcon(contactData.status)}
                        <span className={`ml-2 px-3 py-1 rounded-full text-sm font-medium ${trackingService.getStatusColor(contactData.status)}`}>
                          {contactData.status}
                        </span>
                      </div>
                      <div className={`px-3 py-1 rounded-full text-sm font-medium ${trackingService.getPriorityColor(contactData.priority)}`}>
                        {contactData.priority} Priority
                      </div>
                    </div>

                    {/* Contact Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                        <div className="space-y-3">
                          <div className="flex items-center">
                            <FaUser className="text-gray-400 mr-3" />
                            <span className="text-gray-900">{contactData.name}</span>
                          </div>
                          <div className="flex items-center">
                            <FaEnvelope className="text-gray-400 mr-3" />
                            <span className="text-gray-900">{contactData.email}</span>
                          </div>
                          <div className="flex items-center">
                            <FaPhone className="text-gray-400 mr-3" />
                            <span className="text-gray-900">{contactData.phone}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Inquiry Details</h3>
                        <div className="space-y-3">
                          <div>
                            <span className="text-sm text-gray-500">Subject:</span>
                            <p className="text-gray-900">{contactData.subject}</p>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">Type:</span>
                            <p className="text-gray-900">{contactData.type}</p>
                          </div>
                          <div>
                            <span className="text-sm text-gray-500">Source:</span>
                            <p className="text-gray-900">{contactData.source}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Message */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Message</h3>
                      <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">{contactData.message}</p>
                    </div>

                    {/* Timeline */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Timeline</h3>
                      <div className="space-y-3">
                        <div className="flex items-center">
                          <FaCalendarAlt className="text-gray-400 mr-3" />
                          <span className="text-sm text-gray-500 mr-2">Created:</span>
                          <span className="text-gray-900">{trackingService.formatDate(contactData.createdAt)}</span>
                        </div>
                        <div className="flex items-center">
                          <FaCalendarAlt className="text-gray-400 mr-3" />
                          <span className="text-sm text-gray-500 mr-2">Last Updated:</span>
                          <span className="text-gray-900">{trackingService.formatDate(contactData.lastUpdate)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Notes */}
                    {contactData.note && contactData.note.length > 0 && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Notes & Updates</h3>
                        <div className="space-y-3">
                          {contactData.note.map((note) => (
                            <div key={note.id} className="bg-gray-50 p-4 rounded-lg">
                              <div className="flex justify-between items-start mb-2">
                                <span className={`px-2 py-1 rounded text-xs font-medium ${trackingService.getStatusColor(note.type)}`}>
                                  {note.type}
                                </span>
                                <span className="text-sm text-gray-500">{note.createdAt}</span>
                              </div>
                              <p className="text-gray-700">{note.note}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Internship Data */}
                {internshipData && (
                  <div className="space-y-6">
                    {/* Status */}
                    <div className="flex items-center mb-6">
                      {getStatusIcon(internshipData.applicationStatus)}
                      <span className={`ml-2 px-4 py-2 rounded-full text-lg font-medium ${trackingService.getStatusColor(internshipData.applicationStatus)}`}>
                        {internshipData.applicationStatus}
                      </span>
                    </div>

                    {/* Application Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Applicant Information</h3>
                        <div className="space-y-3">
                          <div className="flex items-center">
                            <FaUser className="text-gray-400 mr-3" />
                            <span className="text-gray-900">{internshipData.name}</span>
                          </div>
                          <div className="flex items-center">
                            <FaEnvelope className="text-gray-400 mr-3" />
                            <span className="text-gray-900">{internshipData.email}</span>
                          </div>
                          <div className="flex items-center">
                            <FaPhone className="text-gray-400 mr-3" />
                            <span className="text-gray-900">{internshipData.phone}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Application Details</h3>
                        <div className="space-y-3">
                          <div className="flex items-center">
                            <FaProjectDiagram className="text-gray-400 mr-3" />
                            <span className="text-gray-900">{internshipData.projectName}</span>
                          </div>
                          <div className="flex items-center">
                            <FaCalendarAlt className="text-gray-400 mr-3" />
                            <span className="text-sm text-gray-500 mr-2">Applied:</span>
                            <span className="text-gray-900">{trackingService.formatDate(internshipData.appliedAt)}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Skills */}
                    {internshipData.skills && internshipData.skills.length > 0 && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Skills</h3>
                        <div className="flex flex-wrap gap-2">
                          {internshipData.skills.map((skill, index) => (
                            <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Links */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">Profile Links</h3>
                      <div className="space-y-2">
                        {internshipData.gitProfile && (
                          <a href={internshipData.gitProfile} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 block">
                            GitHub Profile
                          </a>
                        )}
                        {internshipData.linkeinProfile && (
                          <a href={internshipData.linkeinProfile} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 block">
                            LinkedIn Profile
                          </a>
                        )}
                        {internshipData.portolioLink && (
                          <a href={internshipData.portolioLink} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 block">
                            Portfolio
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default TrackApplication;
