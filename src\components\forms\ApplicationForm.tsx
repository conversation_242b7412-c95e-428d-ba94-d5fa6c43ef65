import React, { useState } from 'react';
import { Button, Input } from '../ui';
import { useApplication } from '../../hooks';

interface ApplicationFormProps {
  projectId: string;
  projectTitle: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const ApplicationForm: React.FC<ApplicationFormProps> = ({
  projectId,
  projectTitle,
  onSuccess,
  onCancel
}) => {
  const { submitApplication, isSubmitting, submitError, submitSuccess } = useApplication();
  
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    university: '',
    course: '',
    year: '',
    cgpa: '',
    programmingExperience: '',
    relevantProjects: '',
    skills: '',
    githubProfile: '',
    portfolioUrl: '',
    whyInterested: '',
    careerGoals: '',
    availability: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    if (!formData.university.trim()) newErrors.university = 'University is required';
    if (!formData.course.trim()) newErrors.course = 'Course is required';
    if (!formData.year.trim()) newErrors.year = 'Year is required';
    if (!formData.whyInterested.trim()) newErrors.whyInterested = 'Please explain why you\'re interested';
    if (!formData.availability.trim()) newErrors.availability = 'Availability is required';

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    console.log(projectId);

    // const applicationData: ApplicationData = {
    //   projectId,
    //   personalInfo: {
    //     fullName: formData.fullName,
    //     email: formData.email,
    //     phone: formData.phone,
    //     university: formData.university,
    //     course: formData.course,
    //     year: formData.year,
    //     cgpa: formData.cgpa
    //   },
    //   experience: {
    //     programmingExperience: formData.programmingExperience,
    //     relevantProjects: formData.relevantProjects,
    //     skills: formData.skills.split(',').map(s => s.trim()).filter(s => s),
    //     githubProfile: formData.githubProfile,
    //     portfolioUrl: formData.portfolioUrl
    //   },
    //   motivation: {
    //     whyInterested: formData.whyInterested,
    //     careerGoals: formData.careerGoals,
    //     availability: formData.availability
    //   }
    // };

    const success = await submitApplication();
    if (success) {
      onSuccess();
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (submitSuccess) {
    return (
      <div className="text-center py-8">
        <div className="text-6xl mb-4">🎉</div>
        <h3 className="text-2xl font-bold text-green-600 mb-4">Application Submitted!</h3>
        <p className="text-gray-600 mb-6">
          Thank you for applying to <strong>{projectTitle}</strong>. 
          We'll review your application and get back to you soon.
        </p>
        <Button onClick={onSuccess} variant="gradient">
          Close
        </Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Apply for: <span className="text-purple-600">{projectTitle}</span>
        </h3>
        <p className="text-gray-600">Fill out the form below to submit your application</p>
      </div>

      {submitError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{submitError}</p>
        </div>
      )}

      {/* Personal Information */}
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-gray-900">Personal Information</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Full Name *"
            value={formData.fullName}
            onChange={(e) => handleInputChange('fullName', e.target.value)}
            error={errors.fullName}
            placeholder="Enter your full name"
          />
          <Input
            label="Email *"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            error={errors.email}
            placeholder="<EMAIL>"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Phone Number *"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            error={errors.phone}
            placeholder="+91 4442116715"
          />
          <Input
            label="University *"
            value={formData.university}
            onChange={(e) => handleInputChange('university', e.target.value)}
            error={errors.university}
            placeholder="Your university name"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            label="Course/Major *"
            value={formData.course}
            onChange={(e) => handleInputChange('course', e.target.value)}
            error={errors.course}
            placeholder="Computer Science"
          />
          <Input
            label="Year *"
            value={formData.year}
            onChange={(e) => handleInputChange('year', e.target.value)}
            error={errors.year}
            placeholder="3rd Year"
          />
          <Input
            label="CGPA/GPA"
            value={formData.cgpa}
            onChange={(e) => handleInputChange('cgpa', e.target.value)}
            placeholder="3.8"
          />
        </div>
      </div>

      {/* Experience */}
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-gray-900">Experience & Skills</h4>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Programming Experience
            </label>
            <textarea
              value={formData.programmingExperience}
              onChange={(e) => handleInputChange('programmingExperience', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Describe your programming experience, languages you know, projects you've worked on..."
            />
          </div>

          <Input
            label="Skills (comma-separated)"
            value={formData.skills}
            onChange={(e) => handleInputChange('skills', e.target.value)}
            placeholder="JavaScript, React, Python, Git, etc."
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="GitHub Profile"
              value={formData.githubProfile}
              onChange={(e) => handleInputChange('githubProfile', e.target.value)}
              placeholder="https://github.com/yourusername"
            />
            <Input
              label="Portfolio URL"
              value={formData.portfolioUrl}
              onChange={(e) => handleInputChange('portfolioUrl', e.target.value)}
              placeholder="https://yourportfolio.com"
            />
          </div>
        </div>
      </div>

      {/* Motivation */}
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-gray-900">Motivation</h4>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Why are you interested in this project? *
          </label>
          <textarea
            value={formData.whyInterested}
            onChange={(e) => handleInputChange('whyInterested', e.target.value)}
            rows={3}
            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
              errors.whyInterested ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Explain what interests you about this project and how it aligns with your goals..."
          />
          {errors.whyInterested && (
            <p className="text-sm text-red-600 mt-1">{errors.whyInterested}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Career Goals
          </label>
          <textarea
            value={formData.careerGoals}
            onChange={(e) => handleInputChange('careerGoals', e.target.value)}
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            placeholder="What are your career aspirations?"
          />
        </div>

        <Input
          label="Availability *"
          value={formData.availability}
          onChange={(e) => handleInputChange('availability', e.target.value)}
          error={errors.availability}
          placeholder="Full-time, Part-time, Weekends, etc."
        />
      </div>

      {/* Submit Buttons */}
      <div className="flex space-x-4 pt-6">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="flex-1"
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="gradient"
          className="flex-1"
          disabled={isSubmitting}
          glow
        >
          {isSubmitting ? 'Submitting...' : 'Submit Application'}
        </Button>
      </div>
    </form>
  );
};

export default ApplicationForm;
