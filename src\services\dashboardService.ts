import apiService, { ApiResponse } from './api';

// Dashboard interfaces
export interface LatestApplicant {
  name: string;
  applicationStatus: 'PENDING' | 'REVIEWING' | 'ACCEPTED' | 'REJECTED';
  projectName: string;
}

export interface DashboardData {
  projectCount: number;
  applicantCount: number;
  activeInternCount: number;
  contactCount: number;
  latestApplicants: LatestApplicant[];
}

class DashboardService {
  // Get dashboard details
  async getDashboardDetails(): Promise<ApiResponse> {
    try {
      const response = await apiService.get<ApiResponse>('/admin/dashboard/details');
      return response;
    } catch (error: any) {
      console.error('Error fetching dashboard details:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch dashboard details');
    }
  }

  // Get application status display name
  getApplicationStatusDisplayName(status: LatestApplicant['applicationStatus']): string {
    switch (status) {
      case 'PENDING':
        return 'Pending';
      case 'REVIEWING':
        return 'Reviewing';
      case 'ACCEPTED':
        return 'Accepted';
      case 'REJECTED':
        return 'Rejected';
      default:
        return status;
    }
  }

  // Get application status color
  getApplicationStatusColor(status: LatestApplicant['applicationStatus']): string {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'REVIEWING':
        return 'bg-blue-100 text-blue-800';
      case 'ACCEPTED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Format dashboard stats for display
  formatDashboardStats(data: DashboardData) {
    return {
      projects: {
        count: data.projectCount,
        label: 'Total Projects',
        icon: 'FaProjectDiagram',
        color: 'blue'
      },
      applicants: {
        count: data.applicantCount,
        label: 'Total Applicants',
        icon: 'FaUsers',
        color: 'purple'
      },
      activeInterns: {
        count: data.activeInternCount,
        label: 'Active Interns',
        icon: 'FaUserGraduate',
        color: 'green'
      },
      contacts: {
        count: data.contactCount,
        label: 'Total Contacts',
        icon: 'FaEnvelope',
        color: 'orange'
      }
    };
  }

  // Get recent activity summary
  getRecentActivitySummary(latestApplicants: LatestApplicant[]) {
    const statusCounts = latestApplicants.reduce((acc, applicant) => {
      acc[applicant.applicationStatus] = (acc[applicant.applicationStatus] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalApplications: latestApplicants.length,
      pendingCount: statusCounts.PENDING || 0,
      reviewingCount: statusCounts.REVIEWING || 0,
      acceptedCount: statusCounts.ACCEPTED || 0,
      rejectedCount: statusCounts.REJECTED || 0,
      statusBreakdown: statusCounts
    };
  }

  // Get unique projects from latest applicants
  getUniqueProjects(latestApplicants: LatestApplicant[]): string[] {
    const projects = new Set(latestApplicants.map(app => app.projectName));
    return Array.from(projects);
  }

  // Filter applicants by status
  filterApplicantsByStatus(
    latestApplicants: LatestApplicant[], 
    status: LatestApplicant['applicationStatus']
  ): LatestApplicant[] {
    return latestApplicants.filter(app => app.applicationStatus === status);
  }

  // Get applicants by project
  getApplicantsByProject(latestApplicants: LatestApplicant[]): Record<string, LatestApplicant[]> {
    return latestApplicants.reduce((acc, applicant) => {
      if (!acc[applicant.projectName]) {
        acc[applicant.projectName] = [];
      }
      acc[applicant.projectName].push(applicant);
      return acc;
    }, {} as Record<string, LatestApplicant[]>);
  }
}

const dashboardService = new DashboardService();
export default dashboardService;
