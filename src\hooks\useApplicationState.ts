import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import applicationService from '../services/applicationService';
import projectService from '../services/projectService';

export type ApplicationStep = 'email' | 'otp' | 'prerequisites' | 'details' | 'success';

export interface ApplicationData {
  projectId: string;
  email: string;
  otpVerified: boolean;
  prerequisites: Record<string, any>;
  studentDetails: {
    personalInfo: {
      fullName: string;
      phone: string;
      dateOfBirth: string;
      address: string;
    };
    academicInfo: {
      university: string;
      course: string;
      year: string;
      cgpa: string;
      graduationDate: string;
    };
    experience: {
      programmingExperience: string;
      relevantProjects: string;
      skills: string[];
      githubProfile?: string;
      portfolioUrl?: string;
      linkedinProfile?: string;
    };
    resume?: File;
  };
  applicationId?: number;
}

interface UseApplicationStateProps {
  projectId: string;
}

const useApplicationState = ({ projectId }: UseApplicationStateProps) => {
  const navigate = useNavigate();

  const [currentStep, setCurrentStep] = useState<ApplicationStep>('email');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  const [applicationData, setApplicationData] = useState<ApplicationData>({
    projectId: projectId || '',
    email: '',
    otpVerified: false,
    prerequisites: {},
    studentDetails: {
      personalInfo: {
        fullName: '',
        phone: '',
        dateOfBirth: '',
        address: ''
      },
      academicInfo: {
        university: '',
        course: '',
        year: '',
        cgpa: '',
        graduationDate: ''
      },
      experience: {
        programmingExperience: '',
        relevantProjects: '',
        skills: [],
        githubProfile: '',
        portfolioUrl: '',
        linkedinProfile: ''
      }
    }
  });

  const scrollToTop = () => {
    setTimeout(() => window.scrollTo({ top: 0, behavior: 'smooth' }), 100);
  };

  const handleApplicationSubmission = async (studentDetails: any) => {
    setIsSubmitting(true);
    setSubmissionError(null);

    try {
      // Get project details to access prerequisite questions
      const project = await projectService.getProjectById(projectId);

      // Prepare the application data for API
      const applicationRequest = {
        name: studentDetails.personalInfo.fullName,
        email: applicationData.email,
        phone: studentDetails.personalInfo.phone,
        projectId: parseInt(projectId),
        internAnswers: applicationService.formatPrerequisiteAnswers(
          applicationData.prerequisites,
          project.prerequisiteQuestions
        ),
        skills: studentDetails.experience.skills,
        linkdinLink: studentDetails.experience.linkedinProfile || '',
        portfolioLink: studentDetails.experience.portfolioUrl || '',
        githubLink: studentDetails.experience.githubProfile || '',
        resume: studentDetails.resume
      };

      console.log('Application request:', applicationRequest);

      // Submit application
      const response = await applicationService.createApplication(applicationRequest);

      // Update application data with submission result
      setApplicationData(prev => ({
        ...prev,
        studentDetails,
        applicationId: response.data.applicationId
      }));

      setCurrentStep('success');
      scrollToTop();
    } catch (error: any) {
      setSubmissionError(error.message || 'Failed to submit application');
      console.error('Application submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStepComplete = async (stepData: any) => {
    switch (currentStep) {
      case 'email':
        setApplicationData(prev => ({ ...prev, email: stepData.email }));
        setCurrentStep('otp');
        scrollToTop();
        break;
      case 'otp':
        setApplicationData(prev => ({ ...prev, otpVerified: true }));
        setCurrentStep('prerequisites');
        scrollToTop();
        break;
      case 'prerequisites':
        console.log('Prerequisites data:', stepData);
        setApplicationData(prev => ({ ...prev, prerequisites: stepData }));
        setCurrentStep('details');
        scrollToTop();
        break;
      case 'details':
        await handleApplicationSubmission(stepData);
        break;
      case 'success':
        navigate('/');
        break;
    }
  };

  const handleBack = () => {
    switch (currentStep) {
      case 'otp':
        setCurrentStep('email');
        scrollToTop();
        break;
      case 'prerequisites':
        setCurrentStep('otp');
        scrollToTop();
        break;
      case 'details':
        setCurrentStep('prerequisites');
        scrollToTop();
        break;
      case 'success':
        setCurrentStep('details');
        scrollToTop();
        break;
      default:
        navigate(`/project/${projectId}`);
    }
  };

  const getCurrentStepIndex = () => {
    const steps = ['email', 'otp', 'prerequisites', 'details', 'success'];
    return steps.findIndex(step => step === currentStep);
  };

  return {
    currentStep,
    applicationData,
    currentStepIndex: getCurrentStepIndex(),
    handleStepComplete,
    handleBack,
    setCurrentStep,
    setApplicationData,
    isSubmitting,
    submissionError
  };
};

export default useApplicationState;
