import React, { useState } from 'react';
import { FaEnvelope, FaArrow<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaCheckCircle } from 'react-icons/fa';
import { <PERSON><PERSON>, ReCaptcha } from '../ui';
import { Project } from '../../hooks';
import { useCaptcha } from '../../hooks';
import applicationService from '../../services/applicationService';

interface EmailStepProps {
  project: Project;
  onNext: (data: { email: string }) => void;
  onBack: () => void;
}

const EmailStep: React.FC<EmailStepProps> = ({ project, onNext, onBack }) => {
  const [email, setEmail] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; captcha?: string }>({});

  // Captcha integration
  const {
    captchaToken,
    captchaError,
    isVerifying,
    isCaptchaEnabled,
    captchaRef,
    handleCaptchaVerify,
    handleCaptchaError,
    handleCaptchaExpired
  } = useCaptcha();

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset errors
    setErrors({});

    // Validate email
    if (!email.trim()) {
      setErrors({ email: 'Email is required' });
      return;
    }

    if (!validateEmail(email)) {
      setErrors({ email: 'Please enter a valid email address' });
      return;
    }

    // Validate captcha (only in production)
    if (isCaptchaEnabled && !captchaToken) {
      setErrors({ captcha: 'Please complete the captcha verification' });
      return;
    }

    setIsValidating(true);

    try {
      // Verify captcha first (only in production)
      // if (isCaptchaEnabled && captchaToken) {
      //   const captchaVerified = await handleCaptchaVerify(captchaToken);
      //   if (!captchaVerified) {
      //     setErrors({ captcha: 'Captcha verification failed. Please try again.' });
      //     return;
      //   }
      // }

      // Send OTP using the real API
      await applicationService.sendOTP(email.trim(), project.id);

      // Reset captcha after successful submission
     // resetCaptcha();

      // If successful, proceed to next step
      onNext({ email: email.trim() });
    } catch (error: any) {
      // Handle specific error cases
      const errorMessage = error.message || 'Failed to send OTP. Please try again.';

      // Check if user already applied
      if (errorMessage.includes('already applied')) {
        setErrors({
          email: 'You have already applied to this project. Please check your email for previous application details.'
        });
      } else {
        setErrors({ email: errorMessage });
      }
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Step Header */}
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <FaEnvelope className="text-white text-2xl" />
        </div>
        <h2 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-3">
          Let's Get Started
        </h2>
        <p className="text-slate-600 text-lg">
          Enter your email address to begin your application for <span className="font-semibold text-indigo-600">{project.title}</span>
        </p>
      </div>

      {/* Email Form */}
      <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6 sm:p-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Input */}
          <div>
            <label htmlFor="email" className="block text-sm font-semibold text-slate-700 mb-2">
              Email Address
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaEnvelope className="h-5 w-5 text-slate-400" />
              </div>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`block w-full pl-10 pr-3 py-3 border rounded-xl shadow-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors ${
                  errors.email 
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                    : 'border-slate-300'
                }`}
                placeholder="Enter your email address"
                disabled={isValidating}
              />
            </div>
            {errors.email && (
              <p className="mt-2 text-sm text-red-600 flex items-center">
                <span className="mr-1">⚠️</span>
                {errors.email}
              </p>
            )}
          </div>

          {/* Benefits Info */}
          <div className="bg-indigo-50 rounded-xl p-4 border border-indigo-200">
            <h3 className="text-sm font-semibold text-indigo-900 mb-3 flex items-center">
              <FaCheckCircle className="mr-2" />
              What happens next?
            </h3>
            <ul className="space-y-2 text-sm text-indigo-700">
              <li className="flex items-start">
                <span className="mr-2 mt-0.5">1.</span>
                <span>We'll send a verification code to your email</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 mt-0.5">2.</span>
                <span>Complete the application prerequisites</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 mt-0.5">3.</span>
                <span>Fill in your personal and academic details</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 mt-0.5">4.</span>
                <span>Submit your application for review</span>
              </li>
            </ul>
          </div>

          {/* reCAPTCHA */}
          <div>
            <ReCaptcha
              ref={captchaRef}
              onVerify={handleCaptchaVerify}
              onError={handleCaptchaError}
              onExpired={handleCaptchaExpired}
              size="normal"
              theme="light"
            />
            {captchaError && (
              <p className="mt-2 text-sm text-red-600 flex items-center">
                <span className="mr-1">⚠️</span>
                {captchaError}
              </p>
            )}
            {errors.captcha && (
              <p className="mt-2 text-sm text-red-600 flex items-center">
                <span className="mr-1">⚠️</span>
                {errors.captcha}
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              className="flex-1 py-3"
              disabled={isValidating}
            >
              <span>Back to Project</span>
            </Button>
            
            <Button
              type="submit"
              variant="gradient"
              className="flex-1 py-3"
              disabled={isValidating || isVerifying || !email.trim() || (isCaptchaEnabled && !captchaToken)}
            >
              <span className="flex items-center justify-center space-x-2">
                {isValidating || isVerifying ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>{isVerifying ? 'Verifying...' : 'Validating...'}</span>
                  </>
                ) : (
                  <>
                    <span>Continue</span>
                    <FaArrowRight className="w-4 h-4" />
                  </>
                )}
              </span>
            </Button>
          </div>
        </form>
      </div>

      {/* Privacy Notice */}
      <div className="mt-6 text-center">
        <p className="text-sm text-slate-500">
          By continuing, you agree to our{' '}
          <a href="#" className="text-indigo-600 hover:text-indigo-700 font-medium">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="#" className="text-indigo-600 hover:text-indigo-700 font-medium">
            Privacy Policy
          </a>
        </p>
      </div>

      {/* Project Info Card */}
      <div className="mt-8 bg-slate-50 rounded-xl p-6 border border-slate-200">
        <div className="flex items-start space-x-4">
          <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
            <FaUser className="text-white text-lg" />
          </div>
          <div>
            <h3 className="font-semibold text-slate-900 mb-1">Applying for: {project.title}</h3>
            <p className="text-slate-600 text-sm mb-2">{project.description}</p>
            <div className="flex items-center space-x-4 text-xs text-slate-500">
              <span>Duration: {project.duration}</span>
              <span>•</span>
              <span>Category: {project.category}</span>
              <span>•</span>
              <span>Status: {project.status}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailStep;
