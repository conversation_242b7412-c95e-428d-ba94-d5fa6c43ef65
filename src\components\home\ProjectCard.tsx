import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ge } from '../ui';
import { Project } from '../../hooks';
import { <PERSON>aUsers, Fa<PERSON>ser<PERSON><PERSON><PERSON>, Fa<PERSON>lock, FaF<PERSON>er, <PERSON>a<PERSON>ool<PERSON>, FaClipboardList } from 'react-icons/fa';

interface ProjectCardProps {
  project: Project;
  onViewDetails: (projectId: string) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project, onViewDetails }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Open':
        return 'success';
      case 'Full':
        return 'warning';
      case 'Closed':
        return 'error';
      default:
        return 'default';
    }
  };


  return (
    <Card hover gradient className="h-full flex flex-col group relative overflow-hidden">
      {/* Gradient overlay on hover */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      {/* Project Image Header */}
      <div className="relative h-48 overflow-hidden">
        {project.image ? (
          <img
            src={project.image}
            alt={`${project.title} - ${project.category} internship project`}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            loading="lazy"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-4xl mb-2">📋</div>
              <div className="text-sm">Project Image</div>
            </div>
          </div>
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>

        {/* Status Badge - Top Right */}
        <div className="absolute top-4 right-4">
          <Badge variant={getStatusColor(project.status)} size="md" glow={project.status === 'ACTIVE'}>
            {project.status}
          </Badge>
        </div>

        {/* Project Title - Bottom */}
        <div className="absolute bottom-4 left-4 right-4">
          <h3 className="text-xl font-bold text-white line-clamp-2 mb-2 drop-shadow-lg">
            {project.title}
          </h3>
          <div className="flex items-center space-x-2 text-sm">
            <div className="flex items-center space-x-1 text-white/90 bg-black/30 px-2 py-1 rounded-full">
              <FaClock className="text-sm" />
              <span className="font-medium">{project.duration}</span>
            </div>
          </div>
        </div>
      </div>

      <CardHeader className="relative pb-2">
        <div className="flex items-center space-x-2 text-sm">
          <div className="flex items-center space-x-1 text-indigo-600 bg-indigo-50 px-3 py-1 rounded-full">
            <FaFolder className="text-sm" />
            <span className="font-medium">{project.category}</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-grow relative">
        <p className="text-gray-700 mb-6 line-clamp-3 leading-relaxed">
          {project.description}
        </p>

        {/* Technologies */}
        <div className="mb-6">
          <h4 className="text-sm font-bold text-gray-900 mb-3 flex items-center">
            <FaTools className="mr-2 text-gray-600" />
            Tech Stack:
          </h4>
          <div className="flex flex-wrap gap-2">
            {project.technologies.slice(0, 4).map((tech, index) => (
              <Badge key={index} variant="purple" size="sm" className="hover:scale-110 transition-transform">
                {tech}
              </Badge>
            ))}
            {project.technologies.length > 4 && (
              <Badge variant="default" size="sm" className="hover:scale-110 transition-transform">
                +{project.technologies.length - 4} more
              </Badge>
            )}
          </div>
        </div>



        {/* Application Stats */}
        <div className="bg-gradient-to-r from-cyan-50 to-blue-50 rounded-xl p-4 space-y-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <FaUsers className="text-blue-600 text-sm" />
              <span className="text-sm font-medium text-gray-700">Openings available:</span>
            </div>
            <span className="font-bold text-purple-600">
              {project.maxInterns}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <FaUserCheck className="text-green-600 text-sm" />
              <span className="text-sm font-medium text-gray-700">Applications received:</span>
            </div>
            <span className="font-bold text-purple-600">
              {project.applicationCount}
            </span>
          </div>
        </div>
      </CardContent>

      <CardFooter className="relative">
        <div className="w-full space-y-4">
          <Button
            variant="gradient"
            size="md"
            className="w-full font-bold flex items-center justify-center space-x-2"
            onClick={() => onViewDetails(project.id)}
            glow
          >
            <FaClipboardList className="text-sm" />
            <span>View Details</span>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default ProjectCard;
