import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info' | 'purple' | 'pink' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  glow?: boolean;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
  glow = false
}) => {
  const baseClasses = 'inline-flex items-center font-semibold rounded-full transition-all duration-300';

  const variantClasses = {
    default: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300',
    success: 'bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-800 border border-emerald-300',
    warning: 'bg-gradient-to-r from-amber-100 to-orange-100 text-amber-800 border border-amber-300',
    error: 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border border-red-300',
    info: 'bg-gradient-to-r from-cyan-100 to-blue-100 text-cyan-800 border border-cyan-300',
    purple: 'bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-800 border border-purple-300',
    pink: 'bg-gradient-to-r from-pink-100 to-rose-100 text-pink-800 border border-pink-300',
    gradient: 'bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 text-white shadow-lg'
  };

  const sizeClasses = {
    sm: 'px-2.5 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const glowClasses = glow ? 'shadow-lg animate-pulse' : '';

  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${glowClasses} ${className}`;

  return (
    <span className={classes}>
      {children}
    </span>
  );
};

export default Badge;
